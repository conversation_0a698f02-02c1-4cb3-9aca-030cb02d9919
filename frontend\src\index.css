@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Dark mode body background */
body.dark {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

body:not(.dark) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Advanced animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(30px) scale(0.95); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%) rotate(-5deg); opacity: 0; }
  to { transform: translateX(0) rotate(0deg); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%) rotate(5deg); opacity: 0; }
  to { transform: translateX(0) rotate(0deg); opacity: 1; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3) rotate(-10deg); opacity: 0; }
  50% { transform: scale(1.05) rotate(5deg); }
  70% { transform: scale(0.9) rotate(-2deg); }
  100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  14% { transform: scale(1.1); }
  28% { transform: scale(1); }
  42% { transform: scale(1.1); }
  70% { transform: scale(1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(2deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(236, 72, 153, 0.5); }
  50% { box-shadow: 0 0 20px rgba(236, 72, 153, 0.8), 0 0 30px rgba(236, 72, 153, 0.6); }
}

@keyframes wiggle {
  0%, 7%, 14%, 21%, 28%, 35%, 42%, 49%, 56%, 63%, 70%, 77%, 84%, 91%, 98%, 100% { transform: rotate(0deg); }
  3.5%, 10.5%, 17.5%, 24.5%, 31.5%, 38.5%, 45.5%, 52.5%, 59.5%, 66.5%, 73.5%, 80.5%, 87.5%, 94.5% { transform: rotate(2deg); }
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Animation classes */
.animate-fadeIn { animation: fadeIn 0.8s ease-out; }
.animate-fade-in { animation: fadeIn 0.8s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.6s ease-out; }
.animate-slideInRight { animation: slideInRight 0.6s ease-out; }
.animate-bounceIn { animation: bounceIn 0.8s ease-out; }
.animate-pulse-custom { animation: pulse 2s infinite; }
.animate-heartbeat { animation: heartbeat 1.5s ease-in-out infinite; }
.animate-float { animation: float 3s ease-in-out infinite; }
.animate-glow { animation: glow 2s ease-in-out infinite; }
.animate-wiggle { animation: wiggle 1s ease-in-out; }

/* Gradient backgrounds - Dark mode compatible */
.love-gradient {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  background-size: 400% 400%;
  animation: rainbow 8s ease infinite;
}

.dark .love-gradient {
  background: linear-gradient(135deg, #7c3aed 0%, #a855f7 50%, #ec4899 100%);
  background-size: 400% 400%;
  animation: rainbow 8s ease infinite;
}

.chat-gradient {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  background-size: 400% 400%;
  animation: rainbow 10s ease infinite;
}

.dark .chat-gradient {
  background: linear-gradient(135deg, #1e40af 0%, #7c3aed 100%);
  background-size: 400% 400%;
  animation: rainbow 10s ease infinite;
}

.photo-gradient {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  background-size: 400% 400%;
  animation: rainbow 12s ease infinite;
}

.dark .photo-gradient {
  background: linear-gradient(135deg, #059669 0%, #7c3aed 100%);
  background-size: 400% 400%;
  animation: rainbow 12s ease infinite;
}

.notes-gradient {
  background: linear-gradient(135deg, #a8caba 0%, #5d4e75 100%);
  background-size: 400% 400%;
  animation: rainbow 14s ease infinite;
}

.dark .notes-gradient {
  background: linear-gradient(135deg, #dc2626 0%, #7c3aed 100%);
  background-size: 400% 400%;
  animation: rainbow 14s ease infinite;
}

/* Glass morphism effect - Dark mode compatible */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.dark .glass {
  background: rgba(17, 24, 39, 0.25);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(75, 85, 99, 0.18);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: linear-gradient(45deg, #f1f1f1, #e1e1e1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #ec4899, #db2777);
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #db2777, #be185d);
}

/* Message bubble animations */
.message-bubble {
  animation: bounceIn 0.5s ease-out;
  transform-origin: bottom;
}

.message-bubble.sent {
  animation: slideInRight 0.4s ease-out;
  transform-origin: bottom right;
}

.message-bubble.received {
  animation: slideInLeft 0.4s ease-out;
  transform-origin: bottom left;
}

/* Button hover effects */
.btn-love {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff6b6b);
  background-size: 200% 200%;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  position: relative;
  overflow: hidden;
}

.btn-love:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 10px 25px rgba(255, 107, 107, 0.6);
  background-position: right center;
}

.btn-love:active {
  transform: translateY(-1px) scale(1.02);
}

.btn-love::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn-love:hover::before {
  left: 100%;
}

/* Floating hearts animation */
@keyframes floatingHearts {
  0% { transform: translateY(0) rotate(0deg) scale(1); opacity: 1; }
  25% { transform: translateY(-25px) rotate(90deg) scale(1.2); }
  50% { transform: translateY(-50px) rotate(180deg) scale(0.8); }
  75% { transform: translateY(-75px) rotate(270deg) scale(1.1); }
  100% { transform: translateY(-100px) rotate(360deg) scale(0); opacity: 0; }
}

.floating-heart {
  animation: floatingHearts 4s ease-in-out infinite;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: linear-gradient(45deg, #ec4899, #db2777);
  animation: typing 1.4s infinite ease-in-out;
  box-shadow: 0 2px 4px rgba(236, 72, 153, 0.3);
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0) rotate(0deg); opacity: 0.5; }
  40% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

/* Photo grid animations */
.photo-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.photo-item:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.photo-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,107,107,0.1), rgba(238,90,36,0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-item:hover::before {
  opacity: 1;
}

/* Shimmer effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Tab animations */
.tab-button {
  position: relative;
  transition: all 0.3s ease;
}

.tab-button::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: linear-gradient(45deg, #ec4899, #db2777);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.tab-button.active::after {
  width: 100%;
}

.tab-button:hover {
  transform: translateY(-2px);
}

/* Note cards */
.note-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.note-card:hover {
  transform: translateY(-5px) rotate(1deg);
  box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.note-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.5s ease;
  opacity: 0;
}

.note-card:hover::before {
  opacity: 1;
  top: -100%;
  left: -100%;
}

/* Loading animations */
.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ec4899;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

.dark .loading-spinner {
  border: 4px solid #374151;
  border-top: 4px solid #a855f7;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Particle effects */
.particle {
  position: absolute;
  pointer-events: none;
  border-radius: 50%;
  animation: particle-float 3s ease-in-out infinite;
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0) scale(1); opacity: 0.7; }
  50% { transform: translateY(-20px) scale(1.2); opacity: 1; }
}

/* Interactive elements */
.interactive-element {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.interactive-element:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.dark .interactive-element:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
}

.interactive-element:active {
  transform: translateY(0);
  box-shadow: 0 5px 10px rgba(0,0,0,0.1);
}

/* Neon glow effects */
.neon-text {
  text-shadow: 0 0 5px #ff6b6b, 0 0 10px #ff6b6b, 0 0 15px #ff6b6b, 0 0 20px #ff6b6b;
  animation: neon-flicker 2s ease-in-out infinite alternate;
}

@keyframes neon-flicker {
  0%, 100% { text-shadow: 0 0 5px #ff6b6b, 0 0 10px #ff6b6b, 0 0 15px #ff6b6b, 0 0 20px #ff6b6b; }
  50% { text-shadow: 0 0 2px #ff6b6b, 0 0 5px #ff6b6b, 0 0 8px #ff6b6b, 0 0 12px #ff6b6b; }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}





{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Heart, Smile, Image, Paperclip, MoreVertical } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Chat = () => {\n  _s();\n  const {\n    user: authUser\n  } = useAuth(); // Renamed to avoid conflict\n  const {\n    darkMode\n  } = useTheme();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Good morning, beautiful! ☀️\",\n    sender: 'other',\n    timestamp: new Date(Date.now() - 3600000),\n    reactions: ['❤️']\n  }, {\n    id: 2,\n    text: \"Good morning, love! Hope you slept well 💕\",\n    sender: 'me',\n    timestamp: new Date(Date.now() - 3500000),\n    reactions: []\n  }]);\n  const [newMessage, setNewMessage] = useState('');\n  const [otherUser, setOtherUser] = useState({\n    id: 2,\n    name: 'Sarah'\n  });\n  const [currentUser, setCurrentUser] = useState({\n    id: 1,\n    name: 'Alex'\n  }); // Renamed from user\n  const [loading, setLoading] = useState(false);\n  const [typing, setTyping] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n  const emojis = ['❤️', '💕', '💖', '💗', '🥰', '😘', '😍', '🌹', '✨', '💫', '🌟', '💎', '🎉', '🔥', '💯', '👑'];\n\n  // Enhanced user avatars with gradients\n  const getUserAvatar = (userId, userName) => {\n    const avatars = {\n      1: {\n        emoji: '👨‍💼',\n        gradient: 'from-blue-500 via-purple-500 to-indigo-600'\n      },\n      2: {\n        emoji: '👩‍💼',\n        gradient: 'from-pink-500 via-rose-500 to-red-500'\n      }\n    };\n    return avatars[userId] || {\n      emoji: '😊',\n      gradient: 'from-gray-400 to-gray-600'\n    };\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    // Simulate typing indicator\n    const timer = setTimeout(() => {\n      setOtherUserTyping(true);\n      setTimeout(() => setOtherUserTyping(false), 3000);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n  const sendMessage = async e => {\n    e.preventDefault();\n    if (!newMessage.trim() || !otherUser) return;\n    const newMsg = {\n      id: messages.length + 1,\n      sender_id: user.id,\n      sender_name: user.name,\n      receiver_id: otherUser.id,\n      receiver_name: otherUser.name,\n      message: newMessage.trim(),\n      created_at: new Date()\n    };\n    setMessages(prev => [...prev, newMsg]);\n    setNewMessage('');\n    setShowEmojiPicker(false);\n    setTyping(false);\n  };\n  const handleTyping = e => {\n    setNewMessage(e.target.value);\n    if (!typing) {\n      setTyping(true);\n    }\n    clearTimeout(typingTimeoutRef.current);\n    typingTimeoutRef.current = setTimeout(() => {\n      setTyping(false);\n    }, 1000);\n  };\n  const addEmoji = emoji => {\n    setNewMessage(prev => prev + emoji);\n    setShowEmojiPicker(false);\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(e);\n    }\n  };\n  const formatTime = date => {\n    return new Date(date).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-lg font-medium\",\n          children: \"Loading your conversation...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 p-4 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute animate-float opacity-20\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 5}s`,\n          animationDuration: `${3 + Math.random() * 4}s`\n        },\n        children: i % 4 === 0 ? '💕' : i % 4 === 1 ? '✨' : i % 4 === 2 ? '🌟' : '💫'\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto relative\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"backdrop-blur-xl bg-white/30 rounded-3xl shadow-2xl border border-white/20 h-[90vh] flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative px-8 py-6 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-sm border-b border-white/10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-16 h-16 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).gradient} flex items-center justify-center text-white text-2xl shadow-xl transform transition-all duration-300 hover:scale-105`,\n                  children: getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).emoji\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 150,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-4 border-white shadow-lg animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-800 mb-1\",\n                  children: (otherUser === null || otherUser === void 0 ? void 0 : otherUser.name) || 'Your Partner'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 156,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 flex items-center\",\n                  children: otherUserTyping ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center text-green-600 font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1 mr-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full animate-bounce\",\n                        style: {\n                          animationDelay: '0s'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 163,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full animate-bounce\",\n                        style: {\n                          animationDelay: '0.1s'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 164,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full animate-bounce\",\n                        style: {\n                          animationDelay: '0.2s'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 165,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 25\n                    }, this), \"Typing...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center text-green-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 25\n                    }, this), \"Active now\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl animate-pulse\",\n                children: \"\\uD83D\\uDC96\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Star, {\n                  className: \"w-6 h-6 text-yellow-500 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-6 h-6 text-purple-500 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform skew-x-12 translate-x-full animate-shimmer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto px-8 py-6 space-y-6 relative\",\n          children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-gray-500 py-20\",\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              className: \"w-20 h-20 mx-auto mb-6 text-gray-300 animate-bounce\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl font-semibold mb-2\",\n              children: \"No messages yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Start your conversation with something sweet!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this) : messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex ${message.sender_id === user.id ? 'justify-end' : 'justify-start'} transform transition-all duration-500 animate-fadeInUp`,\n            style: {\n              animationDelay: `${index * 0.1}s`\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-end space-x-3 max-w-lg ${message.sender_id === user.id ? 'flex-row-reverse space-x-reverse' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(message.sender_id, message.sender_name).gradient} flex items-center justify-center text-white text-lg shadow-lg transform transition-all duration-300 hover:scale-110`,\n                children: getUserAvatar(message.sender_id, message.sender_name).emoji\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-6 py-4 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 ${message.sender_id === user.id ? 'bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white rounded-br-md shadow-purple-200' : 'bg-gradient-to-br from-white to-gray-50 text-gray-800 rounded-bl-md shadow-gray-200 border border-white/50'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm leading-relaxed font-medium\",\n                    children: message.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-xs mt-2 ${message.sender_id === user.id ? 'text-white/70' : 'text-gray-500'}`,\n                    children: formatTime(message.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-2xl transform scale-x-0 group-hover:scale-x-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute ${message.sender_id === user.id ? '-right-2 bottom-4' : '-left-2 bottom-4'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-4 h-4 transform rotate-45 ${message.sender_id === user.id ? 'bg-gradient-to-br from-purple-500 to-rose-500' : 'bg-gradient-to-br from-white to-gray-50 border-l border-b border-white/50'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this)\n          }, message.id || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 17\n          }, this)), otherUserTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-start animate-fadeIn\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).gradient} flex items-center justify-center text-white text-lg shadow-lg animate-pulse`,\n                children: getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).emoji\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-gray-100 to-white px-6 py-4 rounded-2xl rounded-bl-md shadow-xl border border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.4s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 254,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-8 py-6 bg-gradient-to-r from-white/40 to-white/20 backdrop-blur-sm border-t border-white/10 relative\",\n          children: [showEmojiPicker && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-full left-8 right-8 mb-4 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 animate-slideUp\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-8 gap-3\",\n              children: emojis.map((emoji, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => addEmoji(emoji),\n                className: \"text-2xl p-3 rounded-xl hover:bg-pink-100/70 transition-all duration-200 transform hover:scale-125 hover:rotate-12\",\n                style: {\n                  animationDelay: `${index * 0.05}s`\n                },\n                children: emoji\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowEmojiPicker(!showEmojiPicker),\n                className: \"w-12 h-12 rounded-2xl bg-gradient-to-br from-yellow-400 to-orange-500 text-white flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12\",\n                children: /*#__PURE__*/_jsxDEV(Smile, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: newMessage,\n                  onChange: handleTyping,\n                  onKeyPress: handleKeyPress,\n                  placeholder: \"Type something sweet...\",\n                  rows: 1,\n                  className: \"w-full px-6 py-4 bg-white/70 backdrop-blur-sm border-2 border-white/30 rounded-2xl focus:outline-none focus:ring-4 focus:ring-purple-200/50 focus:border-purple-400/50 resize-none transition-all duration-300 text-gray-800 placeholder-gray-500 shadow-inner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-400/10 via-pink-400/10 to-rose-400/10 opacity-0 hover:opacity-100 transition-all duration-300 pointer-events-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 302,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: sendMessage,\n                disabled: !newMessage.trim(),\n                className: \"w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white flex items-center justify-center shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-110 hover:shadow-xl relative overflow-hidden group\",\n                children: [/*#__PURE__*/_jsxDEV(Send, {\n                  className: \"w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 285,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-xs text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Heart, {\n                  className: \"w-3 h-3 text-pink-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Press Enter to send \\u2022 Shift+Enter for new line\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-3 h-3 text-purple-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newMessage.length, \"/500\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-10px) rotate(5deg); }\n        }\n        \n        @keyframes fadeInUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0); \n          }\n        }\n        \n        @keyframes slideUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px) scale(0.95); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0) scale(1); \n          }\n        }\n        \n        @keyframes shimmer {\n          0% { transform: translateX(-100%) skewX(12deg); }\n          100% { transform: translateX(200%) skewX(12deg); }\n        }\n        \n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n        \n        .animate-fadeInUp {\n          animation: fadeInUp 0.6s ease-out forwards;\n        }\n        \n        .animate-slideUp {\n          animation: slideUp 0.3s ease-out forwards;\n        }\n        \n        .animate-shimmer {\n          animation: shimmer 3s infinite;\n        }\n        \n        .animate-fadeIn {\n          animation: fadeInUp 0.3s ease-out forwards;\n        }\n        \n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 8px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: rgba(255, 255, 255, 0.1);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: linear-gradient(to bottom, #ec4899, #be185d);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: linear-gradient(to bottom, #db2777, #9d174d);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n};\n_s(Chat, \"UtLXkmBNYfgXNvKGs8ZYpb1zXUE=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Chat;\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "useTheme", "Send", "Heart", "Smile", "Image", "Paperclip", "MoreVertical", "jsxDEV", "_jsxDEV", "Cha<PERSON>", "_s", "user", "authUser", "darkMode", "messages", "setMessages", "id", "text", "sender", "timestamp", "Date", "now", "reactions", "newMessage", "setNewMessage", "otherUser", "setOtherUser", "name", "currentUser", "setCurrentUser", "loading", "setLoading", "typing", "setTyping", "otherUserTyping", "setOtherUserTyping", "messagesEndRef", "typingTimeoutRef", "emojis", "getUserAvatar", "userId", "userName", "avatars", "emoji", "gradient", "scrollToBottom", "timer", "setTimeout", "clearTimeout", "sendMessage", "e", "preventDefault", "trim", "newMsg", "length", "sender_id", "sender_name", "receiver_id", "receiver_name", "message", "created_at", "prev", "setShowEmojiPicker", "handleTyping", "target", "value", "current", "addEmoji", "_messagesEndRef$curre", "scrollIntoView", "behavior", "handleKeyPress", "key", "shift<PERSON>ey", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "Star", "<PERSON><PERSON><PERSON>", "MessageCircle", "index", "ref", "showEmojiPicker", "onClick", "onChange", "onKeyPress", "placeholder", "rows", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Chat.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Heart, Smile, Image, Paperclip, MoreVertical } from 'lucide-react';\n\nconst Chat = () => {\n  const { user: authUser } = useAuth(); // Renamed to avoid conflict\n  const { darkMode } = useTheme();\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Good morning, beautiful! ☀️\",\n      sender: 'other',\n      timestamp: new Date(Date.now() - 3600000),\n      reactions: ['❤️']\n    },\n    {\n      id: 2,\n      text: \"Good morning, love! Hope you slept well 💕\",\n      sender: 'me',\n      timestamp: new Date(Date.now() - 3500000),\n      reactions: []\n    }\n  ]);\n  \n  const [newMessage, setNewMessage] = useState('');\n  const [otherUser, setOtherUser] = useState({ id: 2, name: '<PERSON>' });\n  const [currentUser, setCurrentUser] = useState({ id: 1, name: '<PERSON>' }); // Renamed from user\n  const [loading, setLoading] = useState(false);\n  const [typing, setTyping] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n\n  const emojis = ['❤️', '💕', '💖', '💗', '🥰', '😘', '😍', '🌹', '✨', '💫', '🌟', '💎', '🎉', '🔥', '💯', '👑'];\n\n  // Enhanced user avatars with gradients\n  const getUserAvatar = (userId, userName) => {\n    const avatars = {\n      1: { emoji: '👨‍💼', gradient: 'from-blue-500 via-purple-500 to-indigo-600' },\n      2: { emoji: '👩‍💼', gradient: 'from-pink-500 via-rose-500 to-red-500' }\n    };\n    return avatars[userId] || { emoji: '😊', gradient: 'from-gray-400 to-gray-600' };\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    // Simulate typing indicator\n    const timer = setTimeout(() => {\n      setOtherUserTyping(true);\n      setTimeout(() => setOtherUserTyping(false), 3000);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const sendMessage = async (e) => {\n    e.preventDefault();\n    if (!newMessage.trim() || !otherUser) return;\n\n    const newMsg = {\n      id: messages.length + 1,\n      sender_id: user.id,\n      sender_name: user.name,\n      receiver_id: otherUser.id,\n      receiver_name: otherUser.name,\n      message: newMessage.trim(),\n      created_at: new Date()\n    };\n\n    setMessages(prev => [...prev, newMsg]);\n    setNewMessage('');\n    setShowEmojiPicker(false);\n    setTyping(false);\n  };\n\n  const handleTyping = (e) => {\n    setNewMessage(e.target.value);\n    \n    if (!typing) {\n      setTyping(true);\n    }\n\n    clearTimeout(typingTimeoutRef.current);\n    typingTimeoutRef.current = setTimeout(() => {\n      setTyping(false);\n    }, 1000);\n  };\n\n  const addEmoji = (emoji) => {\n    setNewMessage(prev => prev + emoji);\n    setShowEmojiPicker(false);\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(e);\n    }\n  };\n\n  const formatTime = (date) => {\n    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 text-lg font-medium\">Loading your conversation...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 p-4 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute animate-float opacity-20\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 5}s`,\n              animationDuration: `${3 + Math.random() * 4}s`\n            }}\n          >\n            {i % 4 === 0 ? '💕' : i % 4 === 1 ? '✨' : i % 4 === 2 ? '🌟' : '💫'}\n          </div>\n        ))}\n      </div>\n\n      <div className=\"max-w-4xl mx-auto relative\">\n        <div className=\"backdrop-blur-xl bg-white/30 rounded-3xl shadow-2xl border border-white/20 h-[90vh] flex flex-col overflow-hidden\">\n          {/* Enhanced Header */}\n          <div className=\"relative px-8 py-6 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-sm border-b border-white/10\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"relative\">\n                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser?.id, otherUser?.name).gradient} flex items-center justify-center text-white text-2xl shadow-xl transform transition-all duration-300 hover:scale-105`}>\n                    {getUserAvatar(otherUser?.id, otherUser?.name).emoji}\n                  </div>\n                  <div className=\"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-4 border-white shadow-lg animate-pulse\"></div>\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-bold text-gray-800 mb-1\">\n                    {otherUser?.name || 'Your Partner'}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 flex items-center\">\n                    {otherUserTyping ? (\n                      <span className=\"flex items-center text-green-600 font-medium\">\n                        <div className=\"flex space-x-1 mr-2\">\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                        Typing...\n                      </span>\n                    ) : (\n                      <span className=\"flex items-center text-green-600\">\n                        <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse\"></div>\n                        Active now\n                      </span>\n                    )}\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-4xl animate-pulse\">💖</div>\n                <div className=\"flex space-x-2\">\n                  <Star className=\"w-6 h-6 text-yellow-500 animate-pulse\" />\n                  <Sparkles className=\"w-6 h-6 text-purple-500 animate-pulse\" />\n                </div>\n              </div>\n            </div>\n\n            {/* Animated background pattern */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform skew-x-12 translate-x-full animate-shimmer\"></div>\n          </div>\n\n          {/* Messages Container */}\n          <div className=\"flex-1 overflow-y-auto px-8 py-6 space-y-6 relative\">\n            {messages.length === 0 ? (\n              <div className=\"text-center text-gray-500 py-20\">\n                <MessageCircle className=\"w-20 h-20 mx-auto mb-6 text-gray-300 animate-bounce\" />\n                <p className=\"text-xl font-semibold mb-2\">No messages yet</p>\n                <p className=\"text-gray-400\">Start your conversation with something sweet!</p>\n              </div>\n            ) : (\n              messages.map((message, index) => (\n                <div\n                  key={message.id || index}\n                  className={`flex ${message.sender_id === user.id ? 'justify-end' : 'justify-start'} transform transition-all duration-500 animate-fadeInUp`}\n                  style={{ animationDelay: `${index * 0.1}s` }}\n                >\n                  <div className={`flex items-end space-x-3 max-w-lg ${message.sender_id === user.id ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                    <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(message.sender_id, message.sender_name).gradient} flex items-center justify-center text-white text-lg shadow-lg transform transition-all duration-300 hover:scale-110`}>\n                      {getUserAvatar(message.sender_id, message.sender_name).emoji}\n                    </div>\n                    \n                    <div className=\"relative group\">\n                      <div\n                        className={`px-6 py-4 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 ${\n                          message.sender_id === user.id\n                            ? 'bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white rounded-br-md shadow-purple-200'\n                            : 'bg-gradient-to-br from-white to-gray-50 text-gray-800 rounded-bl-md shadow-gray-200 border border-white/50'\n                        }`}\n                      >\n                        <p className=\"text-sm leading-relaxed font-medium\">{message.message}</p>\n                        <p className={`text-xs mt-2 ${message.sender_id === user.id ? 'text-white/70' : 'text-gray-500'}`}>\n                          {formatTime(message.created_at)}\n                        </p>\n                        \n                        {/* Message hover effect */}\n                        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-2xl transform scale-x-0 group-hover:scale-x-100\"></div>\n                      </div>\n                      \n                      {/* Message tail */}\n                      <div className={`absolute ${message.sender_id === user.id ? '-right-2 bottom-4' : '-left-2 bottom-4'}`}>\n                        <div className={`w-4 h-4 transform rotate-45 ${\n                          message.sender_id === user.id\n                            ? 'bg-gradient-to-br from-purple-500 to-rose-500'\n                            : 'bg-gradient-to-br from-white to-gray-50 border-l border-b border-white/50'\n                        }`}></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n            \n            {/* Enhanced typing indicator */}\n            {otherUserTyping && (\n              <div className=\"flex justify-start animate-fadeIn\">\n                <div className=\"flex items-end space-x-3\">\n                  <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser?.id, otherUser?.name).gradient} flex items-center justify-center text-white text-lg shadow-lg animate-pulse`}>\n                    {getUserAvatar(otherUser?.id, otherUser?.name).emoji}\n                  </div>\n                  <div className=\"bg-gradient-to-r from-gray-100 to-white px-6 py-4 rounded-2xl rounded-bl-md shadow-xl border border-gray-200\">\n                    <div className=\"flex space-x-2\">\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.4s'}}></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Enhanced Message Input */}\n          <div className=\"px-8 py-6 bg-gradient-to-r from-white/40 to-white/20 backdrop-blur-sm border-t border-white/10 relative\">\n            {/* Emoji Picker */}\n            {showEmojiPicker && (\n              <div className=\"absolute bottom-full left-8 right-8 mb-4 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 animate-slideUp\">\n                <div className=\"grid grid-cols-8 gap-3\">\n                  {emojis.map((emoji, index) => (\n                    <button\n                      key={index}\n                      onClick={() => addEmoji(emoji)}\n                      className=\"text-2xl p-3 rounded-xl hover:bg-pink-100/70 transition-all duration-200 transform hover:scale-125 hover:rotate-12\"\n                      style={{ animationDelay: `${index * 0.05}s` }}\n                    >\n                      {emoji}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-end space-x-4\">\n                <button\n                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                  className=\"w-12 h-12 rounded-2xl bg-gradient-to-br from-yellow-400 to-orange-500 text-white flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12\"\n                >\n                  <Smile className=\"w-6 h-6\" />\n                </button>\n                \n                <div className=\"flex-1 relative\">\n                  <textarea\n                    value={newMessage}\n                    onChange={handleTyping}\n                    onKeyPress={handleKeyPress}\n                    placeholder=\"Type something sweet...\"\n                    rows={1}\n                    className=\"w-full px-6 py-4 bg-white/70 backdrop-blur-sm border-2 border-white/30 rounded-2xl focus:outline-none focus:ring-4 focus:ring-purple-200/50 focus:border-purple-400/50 resize-none transition-all duration-300 text-gray-800 placeholder-gray-500 shadow-inner\"\n                  />\n                  <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-400/10 via-pink-400/10 to-rose-400/10 opacity-0 hover:opacity-100 transition-all duration-300 pointer-events-none\"></div>\n                </div>\n                \n                <button\n                  onClick={sendMessage}\n                  disabled={!newMessage.trim()}\n                  className=\"w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white flex items-center justify-center shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-110 hover:shadow-xl relative overflow-hidden group\"\n                >\n                  <Send className=\"w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200\" />\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform translate-x-full group-hover:translate-x-0 transition-transform duration-500\"></div>\n                </button>\n              </div>\n              \n              <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                <span className=\"flex items-center space-x-2\">\n                  <Heart className=\"w-3 h-3 text-pink-400\" />\n                  <span>Press Enter to send • Shift+Enter for new line</span>\n                </span>\n                <span className=\"flex items-center space-x-1\">\n                  <Sparkles className=\"w-3 h-3 text-purple-400\" />\n                  <span>{newMessage.length}/500</span>\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-10px) rotate(5deg); }\n        }\n        \n        @keyframes fadeInUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0); \n          }\n        }\n        \n        @keyframes slideUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px) scale(0.95); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0) scale(1); \n          }\n        }\n        \n        @keyframes shimmer {\n          0% { transform: translateX(-100%) skewX(12deg); }\n          100% { transform: translateX(200%) skewX(12deg); }\n        }\n        \n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n        \n        .animate-fadeInUp {\n          animation: fadeInUp 0.6s ease-out forwards;\n        }\n        \n        .animate-slideUp {\n          animation: slideUp 0.3s ease-out forwards;\n        }\n        \n        .animate-shimmer {\n          animation: shimmer 3s infinite;\n        }\n        \n        .animate-fadeIn {\n          animation: fadeInUp 0.3s ease-out forwards;\n        }\n        \n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 8px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: rgba(255, 255, 255, 0.1);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: linear-gradient(to bottom, #ec4899, #be185d);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: linear-gradient(to bottom, #db2777, #9d174d);\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Chat;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElF,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI,EAAEC;EAAS,CAAC,GAAGb,OAAO,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM;IAAEc;EAAS,CAAC,GAAGb,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,CACvC;IACEoB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,6BAA6B;IACnCC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE,CAAC,IAAI;EAClB,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,4CAA4C;IAClDC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC;IAAEoB,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAE;EAAQ,CAAC,CAAC;EACpE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC;IAAEoB,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAE;EAAO,CAAC,CAAC,CAAC,CAAC;EACzE,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACoC,MAAM,EAAEC,SAAS,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsC,eAAe,EAAEC,kBAAkB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAMwC,cAAc,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMuC,gBAAgB,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAErC,MAAMwC,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;EAE9G;EACA,MAAMC,aAAa,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,CAAC,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAA6C,CAAC;MAC7E,CAAC,EAAE;QAAED,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAwC;IACzE,CAAC;IACD,OAAOF,OAAO,CAACF,MAAM,CAAC,IAAI;MAAEG,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAA4B,CAAC;EAClF,CAAC;EAED/C,SAAS,CAAC,MAAM;IACdgD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC/B,QAAQ,CAAC,CAAC;EAEdjB,SAAS,CAAC,MAAM;IACd;IACA,MAAMiD,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BZ,kBAAkB,CAAC,IAAI,CAAC;MACxBY,UAAU,CAAC,MAAMZ,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACnD,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMa,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC5B,UAAU,CAAC6B,IAAI,CAAC,CAAC,IAAI,CAAC3B,SAAS,EAAE;IAEtC,MAAM4B,MAAM,GAAG;MACbrC,EAAE,EAAEF,QAAQ,CAACwC,MAAM,GAAG,CAAC;MACvBC,SAAS,EAAE5C,IAAI,CAACK,EAAE;MAClBwC,WAAW,EAAE7C,IAAI,CAACgB,IAAI;MACtB8B,WAAW,EAAEhC,SAAS,CAACT,EAAE;MACzB0C,aAAa,EAAEjC,SAAS,CAACE,IAAI;MAC7BgC,OAAO,EAAEpC,UAAU,CAAC6B,IAAI,CAAC,CAAC;MAC1BQ,UAAU,EAAE,IAAIxC,IAAI,CAAC;IACvB,CAAC;IAEDL,WAAW,CAAC8C,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,MAAM,CAAC,CAAC;IACtC7B,aAAa,CAAC,EAAE,CAAC;IACjBsC,kBAAkB,CAAC,KAAK,CAAC;IACzB7B,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAM8B,YAAY,GAAIb,CAAC,IAAK;IAC1B1B,aAAa,CAAC0B,CAAC,CAACc,MAAM,CAACC,KAAK,CAAC;IAE7B,IAAI,CAACjC,MAAM,EAAE;MACXC,SAAS,CAAC,IAAI,CAAC;IACjB;IAEAe,YAAY,CAACX,gBAAgB,CAAC6B,OAAO,CAAC;IACtC7B,gBAAgB,CAAC6B,OAAO,GAAGnB,UAAU,CAAC,MAAM;MAC1Cd,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMkC,QAAQ,GAAIxB,KAAK,IAAK;IAC1BnB,aAAa,CAACqC,IAAI,IAAIA,IAAI,GAAGlB,KAAK,CAAC;IACnCmB,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMjB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAuB,qBAAA;IAC3B,CAAAA,qBAAA,GAAAhC,cAAc,CAAC8B,OAAO,cAAAE,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,cAAc,GAAIrB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACsB,GAAG,KAAK,OAAO,IAAI,CAACtB,CAAC,CAACuB,QAAQ,EAAE;MACpCvB,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBF,WAAW,CAACC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMwB,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIvD,IAAI,CAACuD,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACtF,CAAC;EAED,IAAIhD,OAAO,EAAE;IACX,oBACEtB,OAAA;MAAKuE,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChHxE,OAAA;QAAKuE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BxE,OAAA;UAAKuE,SAAS,EAAC;QAA6F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnH5E,OAAA;UAAGuE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE5E,OAAA;IAAKuE,SAAS,EAAC,mGAAmG;IAAAC,QAAA,gBAEhHxE,OAAA;MAAKuE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClE,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBhF,OAAA;QAEEuE,SAAS,EAAC,mCAAmC;QAC7CU,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C,CAAE;QAAAZ,QAAA,EAEDQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG;MAAI,GAT9DA,CAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUH,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN5E,OAAA;MAAKuE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCxE,OAAA;QAAKuE,SAAS,EAAC,mHAAmH;QAAAC,QAAA,gBAEhIxE,OAAA;UAAKuE,SAAS,EAAC,mIAAmI;UAAAC,QAAA,gBAChJxE,OAAA;YAAKuE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDxE,OAAA;cAAKuE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxE,OAAA;gBAAKuE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBxE,OAAA;kBAAKuE,SAAS,EAAE,2CAA2CxC,aAAa,CAACd,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACiB,QAAQ,uHAAwH;kBAAAoC,QAAA,EACtOzC,aAAa,CAACd,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACgB;gBAAK;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN5E,OAAA;kBAAKuE,SAAS,EAAC;gBAA6G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChI,CAAC,eACN5E,OAAA;gBAAAwE,QAAA,gBACExE,OAAA;kBAAIuE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClD,CAAAvD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,KAAI;gBAAc;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACL5E,OAAA;kBAAGuE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACnD9C,eAAe,gBACd1B,OAAA;oBAAMuE,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,gBAC5DxE,OAAA;sBAAKuE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBAClCxE,OAAA;wBAAKuE,SAAS,EAAC,kDAAkD;wBAACU,KAAK,EAAE;0BAACK,cAAc,EAAE;wBAAI;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvG5E,OAAA;wBAAKuE,SAAS,EAAC,kDAAkD;wBAACU,KAAK,EAAE;0BAACK,cAAc,EAAE;wBAAM;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzG5E,OAAA;wBAAKuE,SAAS,EAAC,kDAAkD;wBAACU,KAAK,EAAE;0BAACK,cAAc,EAAE;wBAAM;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG,CAAC,aAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAEP5E,OAAA;oBAAMuE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAChDxE,OAAA;sBAAKuE,SAAS,EAAC;oBAAsD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,cAE9E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5E,OAAA;cAAKuE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CxE,OAAA;gBAAKuE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD5E,OAAA;gBAAKuE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BxE,OAAA,CAACwF,IAAI;kBAACjB,SAAS,EAAC;gBAAuC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1D5E,OAAA,CAACyF,QAAQ;kBAAClB,SAAS,EAAC;gBAAuC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5E,OAAA;YAAKuE,SAAS,EAAC;UAAoI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvJ,CAAC,eAGN5E,OAAA;UAAKuE,SAAS,EAAC,qDAAqD;UAAAC,QAAA,GACjElE,QAAQ,CAACwC,MAAM,KAAK,CAAC,gBACpB9C,OAAA;YAAKuE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CxE,OAAA,CAAC0F,aAAa;cAACnB,SAAS,EAAC;YAAqD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjF5E,OAAA;cAAGuE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7D5E,OAAA;cAAGuE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,GAENtE,QAAQ,CAACwE,GAAG,CAAC,CAAC3B,OAAO,EAAEwC,KAAK,kBAC1B3F,OAAA;YAEEuE,SAAS,EAAE,QAAQpB,OAAO,CAACJ,SAAS,KAAK5C,IAAI,CAACK,EAAE,GAAG,aAAa,GAAG,eAAe,yDAA0D;YAC5IyE,KAAK,EAAE;cAAEK,cAAc,EAAE,GAAGK,KAAK,GAAG,GAAG;YAAI,CAAE;YAAAnB,QAAA,eAE7CxE,OAAA;cAAKuE,SAAS,EAAE,qCAAqCpB,OAAO,CAACJ,SAAS,KAAK5C,IAAI,CAACK,EAAE,GAAG,kCAAkC,GAAG,EAAE,EAAG;cAAAgE,QAAA,gBAC7HxE,OAAA;gBAAKuE,SAAS,EAAE,2CAA2CxC,aAAa,CAACoB,OAAO,CAACJ,SAAS,EAAEI,OAAO,CAACH,WAAW,CAAC,CAACZ,QAAQ,sHAAuH;gBAAAoC,QAAA,EAC7OzC,aAAa,CAACoB,OAAO,CAACJ,SAAS,EAAEI,OAAO,CAACH,WAAW,CAAC,CAACb;cAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAEN5E,OAAA;gBAAKuE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BxE,OAAA;kBACEuE,SAAS,EAAE,yFACTpB,OAAO,CAACJ,SAAS,KAAK5C,IAAI,CAACK,EAAE,GACzB,uGAAuG,GACvG,4GAA4G,EAC/G;kBAAAgE,QAAA,gBAEHxE,OAAA;oBAAGuE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAErB,OAAO,CAACA;kBAAO;oBAAAsB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxE5E,OAAA;oBAAGuE,SAAS,EAAE,gBAAgBpB,OAAO,CAACJ,SAAS,KAAK5C,IAAI,CAACK,EAAE,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAgE,QAAA,EAC/FN,UAAU,CAACf,OAAO,CAACC,UAAU;kBAAC;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eAGJ5E,OAAA;oBAAKuE,SAAS,EAAC;kBAAsM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzN,CAAC,eAGN5E,OAAA;kBAAKuE,SAAS,EAAE,YAAYpB,OAAO,CAACJ,SAAS,KAAK5C,IAAI,CAACK,EAAE,GAAG,mBAAmB,GAAG,kBAAkB,EAAG;kBAAAgE,QAAA,eACrGxE,OAAA;oBAAKuE,SAAS,EAAE,+BACdpB,OAAO,CAACJ,SAAS,KAAK5C,IAAI,CAACK,EAAE,GACzB,+CAA+C,GAC/C,2EAA2E;kBAC9E;oBAAAiE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAnCDzB,OAAO,CAAC3C,EAAE,IAAImF,KAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCrB,CACN,CACF,EAGAlD,eAAe,iBACd1B,OAAA;YAAKuE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDxE,OAAA;cAAKuE,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCxE,OAAA;gBAAKuE,SAAS,EAAE,2CAA2CxC,aAAa,CAACd,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACiB,QAAQ,8EAA+E;gBAAAoC,QAAA,EAC7LzC,aAAa,CAACd,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACgB;cAAK;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN5E,OAAA;gBAAKuE,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAC3HxE,OAAA;kBAAKuE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BxE,OAAA;oBAAKuE,SAAS,EAAC,iDAAiD;oBAACU,KAAK,EAAE;sBAACK,cAAc,EAAE;oBAAI;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtG5E,OAAA;oBAAKuE,SAAS,EAAC,iDAAiD;oBAACU,KAAK,EAAE;sBAACK,cAAc,EAAE;oBAAM;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxG5E,OAAA;oBAAKuE,SAAS,EAAC,iDAAiD;oBAACU,KAAK,EAAE;sBAACK,cAAc,EAAE;oBAAM;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED5E,OAAA;YAAK4F,GAAG,EAAEhE;UAAe;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGN5E,OAAA;UAAKuE,SAAS,EAAC,yGAAyG;UAAAC,QAAA,GAErHqB,eAAe,iBACd7F,OAAA;YAAKuE,SAAS,EAAC,yIAAyI;YAAAC,QAAA,eACtJxE,OAAA;cAAKuE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpC1C,MAAM,CAACgD,GAAG,CAAC,CAAC3C,KAAK,EAAEwD,KAAK,kBACvB3F,OAAA;gBAEE8F,OAAO,EAAEA,CAAA,KAAMnC,QAAQ,CAACxB,KAAK,CAAE;gBAC/BoC,SAAS,EAAC,oHAAoH;gBAC9HU,KAAK,EAAE;kBAAEK,cAAc,EAAE,GAAGK,KAAK,GAAG,IAAI;gBAAI,CAAE;gBAAAnB,QAAA,EAE7CrC;cAAK,GALDwD,KAAK;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED5E,OAAA;YAAKuE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBxE,OAAA;cAAKuE,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCxE,OAAA;gBACE8F,OAAO,EAAEA,CAAA,KAAMxC,kBAAkB,CAAC,CAACuC,eAAe,CAAE;gBACpDtB,SAAS,EAAC,mNAAmN;gBAAAC,QAAA,eAE7NxE,OAAA,CAACL,KAAK;kBAAC4E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAET5E,OAAA;gBAAKuE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BxE,OAAA;kBACEyD,KAAK,EAAE1C,UAAW;kBAClBgF,QAAQ,EAAExC,YAAa;kBACvByC,UAAU,EAAEjC,cAAe;kBAC3BkC,WAAW,EAAC,yBAAyB;kBACrCC,IAAI,EAAE,CAAE;kBACR3B,SAAS,EAAC;gBAAgQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3Q,CAAC,eACF5E,OAAA;kBAAKuE,SAAS,EAAC;gBAA6K;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChM,CAAC,eAEN5E,OAAA;gBACE8F,OAAO,EAAErD,WAAY;gBACrB0D,QAAQ,EAAE,CAACpF,UAAU,CAAC6B,IAAI,CAAC,CAAE;gBAC7B2B,SAAS,EAAC,6RAA6R;gBAAAC,QAAA,gBAEvSxE,OAAA,CAACP,IAAI;kBAAC8E,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClG5E,OAAA;kBAAKuE,SAAS,EAAC;gBAAuK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5E,OAAA;cAAKuE,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtExE,OAAA;gBAAMuE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC3CxE,OAAA,CAACN,KAAK;kBAAC6E,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3C5E,OAAA;kBAAAwE,QAAA,EAAM;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACP5E,OAAA;gBAAMuE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC3CxE,OAAA,CAACyF,QAAQ;kBAAClB,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChD5E,OAAA;kBAAAwE,QAAA,GAAOzD,UAAU,CAAC+B,MAAM,EAAC,MAAI;gBAAA;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN5E,OAAA;MAAOoG,GAAG;MAAA5B,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC1E,EAAA,CA9YID,IAAI;EAAA,QACmBV,OAAO,EACbC,QAAQ;AAAA;AAAA6G,EAAA,GAFzBpG,IAAI;AAgZV,eAAeA,IAAI;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Heart, Smile, Image, Paperclip, MoreVertical, Star, Sparkles, MessageCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Chat = () => {\n  _s();\n  const {\n    user: authUser\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Good morning, beautiful! ☀️\",\n    sender: 'other',\n    timestamp: new Date(Date.now() - 3600000),\n    reactions: ['❤️']\n  }, {\n    id: 2,\n    text: \"Good morning, love! Hope you slept well 💕\",\n    sender: 'me',\n    timestamp: new Date(Date.now() - 3500000),\n    reactions: []\n  }]);\n  const [newMessage, setNewMessage] = useState('');\n  const [otherUser, setOtherUser] = useState({\n    id: 2,\n    name: 'Sarah'\n  });\n  const [currentUser, setCurrentUser] = useState({\n    id: 1,\n    name: 'Alex'\n  });\n  const [loading, setLoading] = useState(false);\n  const [typing, setTyping] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n  const emojis = ['❤️', '💕', '💖', '💗', '🥰', '😘', '😍', '🌹', '✨', '💫', '🌟', '💎', '🎉', '🔥', '💯', '👑'];\n\n  // Use authUser instead of user\n  const user = authUser;\n\n  // Enhanced user avatars with gradients\n  const getUserAvatar = (userId, userName) => {\n    const avatars = {\n      1: {\n        emoji: '👨‍💼',\n        gradient: 'from-blue-500 via-purple-500 to-indigo-600'\n      },\n      2: {\n        emoji: '👩‍💼',\n        gradient: 'from-pink-500 via-rose-500 to-red-500'\n      }\n    };\n    return avatars[userId] || {\n      emoji: '😊',\n      gradient: 'from-gray-400 to-gray-600'\n    };\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    // Simulate typing indicator\n    const timer = setTimeout(() => {\n      setOtherUserTyping(true);\n      setTimeout(() => setOtherUserTyping(false), 3000);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n  const sendMessage = async e => {\n    e.preventDefault();\n    if (!newMessage.trim() || !otherUser) return;\n    const newMsg = {\n      id: messages.length + 1,\n      sender_id: user.id,\n      sender_name: user.name,\n      receiver_id: otherUser.id,\n      receiver_name: otherUser.name,\n      message: newMessage.trim(),\n      created_at: new Date()\n    };\n    setMessages(prev => [...prev, newMsg]);\n    setNewMessage('');\n    setShowEmojiPicker(false);\n    setTyping(false);\n  };\n  const handleTyping = e => {\n    setNewMessage(e.target.value);\n    if (!typing) {\n      setTyping(true);\n    }\n    clearTimeout(typingTimeoutRef.current);\n    typingTimeoutRef.current = setTimeout(() => {\n      setTyping(false);\n    }, 1000);\n  };\n  const addEmoji = emoji => {\n    setNewMessage(prev => prev + emoji);\n    setShowEmojiPicker(false);\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(e);\n    }\n  };\n  const formatTime = date => {\n    return new Date(date).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-lg font-medium\",\n          children: \"Loading your conversation...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen transition-colors duration-300 ${darkMode ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' : 'bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto p-4 h-screen flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'} backdrop-blur-sm rounded-t-2xl p-4 border-b shadow-lg`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse\",\n              children: \"\\uD83D\\uDC95\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: `text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n                children: otherUser.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n                children: otherUserTyping ? 'Typing...' : 'Online'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex-1 ${darkMode ? 'bg-gray-800/50' : 'bg-white/50'} backdrop-blur-sm overflow-y-auto p-4 space-y-4 custom-scrollbar`,\n        children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-lg ${message.sender === 'me' ? darkMode ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white' : 'bg-gradient-to-r from-pink-500 to-rose-500 text-white' : darkMode ? 'bg-gray-700 text-gray-100 border border-gray-600' : 'bg-white text-gray-800 border border-gray-200'} relative group`,\n            children: editingMessage === message.id ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: editContent,\n                onChange: e => setEditContent(e.target.value),\n                onKeyPress: handleEditKeyPress,\n                className: `w-full p-2 rounded-lg resize-none ${darkMode ? 'bg-gray-600 text-white' : 'bg-gray-100 text-gray-800'}`,\n                rows: 2\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSaveEdit,\n                  className: \"px-3 py-1 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600\",\n                  children: /*#__PURE__*/_jsxDEV(Save, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleCancelEdit,\n                  className: \"px-3 py-1 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600\",\n                  children: /*#__PURE__*/_jsxDEV(X, {\n                    size: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 195,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm leading-relaxed\",\n                children: message.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mt-2\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs ${message.sender === 'me' ? 'text-white/70' : darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                  children: formatTime(message.timestamp)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'} backdrop-blur-sm rounded-b-2xl p-4 border-t`,\n        children: /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: sendMessage,\n          className: \"flex items-end space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative\",\n            children: /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: newMessage,\n              onChange: e => setNewMessage(e.target.value),\n              onKeyPress: handleKeyPress,\n              placeholder: \"Type your love message... \\uD83D\\uDC95\",\n              rows: 1,\n              className: `w-full px-4 py-3 rounded-xl resize-none focus:outline-none focus:ring-2 ${darkMode ? 'bg-gray-700 text-white border-gray-600 focus:ring-purple-500 placeholder-gray-400' : 'bg-pink-50 text-gray-800 border-pink-200 focus:ring-pink-300 placeholder-pink-400'} border-2 transition-all duration-300`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !newMessage.trim(),\n            className: `px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 ${darkMode ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700' : 'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600'} text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105`,\n            children: /*#__PURE__*/_jsxDEV(Send, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(Chat, \"Mk7QZ4jUQdYA+IofGoP0HC+Unbc=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Chat;\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "useTheme", "Send", "Heart", "Smile", "Image", "Paperclip", "MoreVertical", "Star", "<PERSON><PERSON><PERSON>", "MessageCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Cha<PERSON>", "_s", "user", "authUser", "darkMode", "messages", "setMessages", "id", "text", "sender", "timestamp", "Date", "now", "reactions", "newMessage", "setNewMessage", "otherUser", "setOtherUser", "name", "currentUser", "setCurrentUser", "loading", "setLoading", "typing", "setTyping", "otherUserTyping", "setOtherUserTyping", "showEmojiPicker", "setShowEmojiPicker", "messagesEndRef", "typingTimeoutRef", "emojis", "getUserAvatar", "userId", "userName", "avatars", "emoji", "gradient", "scrollToBottom", "timer", "setTimeout", "clearTimeout", "sendMessage", "e", "preventDefault", "trim", "newMsg", "length", "sender_id", "sender_name", "receiver_id", "receiver_name", "message", "created_at", "prev", "handleTyping", "target", "value", "current", "addEmoji", "_messagesEndRef$curre", "scrollIntoView", "behavior", "handleKeyPress", "key", "shift<PERSON>ey", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "editingMessage", "<PERSON><PERSON><PERSON><PERSON>", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onKeyPress", "handleEditKeyPress", "rows", "onClick", "handleSaveEdit", "Save", "size", "handleCancelEdit", "X", "ref", "onSubmit", "placeholder", "type", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Chat.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Heart, Smile, Image, Paperclip, MoreVertical, Star, Sparkles, MessageCircle } from 'lucide-react';\n\nconst Chat = () => {\n  const { user: authUser } = useAuth();\n  const { darkMode } = useTheme();\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Good morning, beautiful! ☀️\",\n      sender: 'other',\n      timestamp: new Date(Date.now() - 3600000),\n      reactions: ['❤️']\n    },\n    {\n      id: 2,\n      text: \"Good morning, love! Hope you slept well 💕\",\n      sender: 'me',\n      timestamp: new Date(Date.now() - 3500000),\n      reactions: []\n    }\n  ]);\n  \n  const [newMessage, setNewMessage] = useState('');\n  const [otherUser, setOtherUser] = useState({ id: 2, name: '<PERSON>' });\n  const [currentUser, setCurrentUser] = useState({ id: 1, name: '<PERSON>' });\n  const [loading, setLoading] = useState(false);\n  const [typing, setTyping] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n\n  const emojis = ['❤️', '💕', '💖', '💗', '🥰', '😘', '😍', '🌹', '✨', '💫', '🌟', '💎', '🎉', '🔥', '💯', '👑'];\n\n  // Use authUser instead of user\n  const user = authUser;\n\n  // Enhanced user avatars with gradients\n  const getUserAvatar = (userId, userName) => {\n    const avatars = {\n      1: { emoji: '👨‍💼', gradient: 'from-blue-500 via-purple-500 to-indigo-600' },\n      2: { emoji: '👩‍💼', gradient: 'from-pink-500 via-rose-500 to-red-500' }\n    };\n    return avatars[userId] || { emoji: '😊', gradient: 'from-gray-400 to-gray-600' };\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    // Simulate typing indicator\n    const timer = setTimeout(() => {\n      setOtherUserTyping(true);\n      setTimeout(() => setOtherUserTyping(false), 3000);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const sendMessage = async (e) => {\n    e.preventDefault();\n    if (!newMessage.trim() || !otherUser) return;\n\n    const newMsg = {\n      id: messages.length + 1,\n      sender_id: user.id,\n      sender_name: user.name,\n      receiver_id: otherUser.id,\n      receiver_name: otherUser.name,\n      message: newMessage.trim(),\n      created_at: new Date()\n    };\n\n    setMessages(prev => [...prev, newMsg]);\n    setNewMessage('');\n    setShowEmojiPicker(false);\n    setTyping(false);\n  };\n\n  const handleTyping = (e) => {\n    setNewMessage(e.target.value);\n    \n    if (!typing) {\n      setTyping(true);\n    }\n\n    clearTimeout(typingTimeoutRef.current);\n    typingTimeoutRef.current = setTimeout(() => {\n      setTyping(false);\n    }, 1000);\n  };\n\n  const addEmoji = (emoji) => {\n    setNewMessage(prev => prev + emoji);\n    setShowEmojiPicker(false);\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(e);\n    }\n  };\n\n  const formatTime = (date) => {\n    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 text-lg font-medium\">Loading your conversation...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${\n      darkMode \n        ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' \n        : 'bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50'\n    }`}>\n      <div className=\"max-w-4xl mx-auto p-4 h-screen flex flex-col\">\n        {/* Chat Header */}\n        <div className={`${\n          darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'\n        } backdrop-blur-sm rounded-t-2xl p-4 border-b shadow-lg`}>\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse\">\n                💕\n              </div>\n              <div>\n                <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n                  {otherUser.name}\n                </h2>\n                <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                  {otherUserTyping ? 'Typing...' : 'Online'}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Messages Container */}\n        <div className={`flex-1 ${\n          darkMode ? 'bg-gray-800/50' : 'bg-white/50'\n        } backdrop-blur-sm overflow-y-auto p-4 space-y-4 custom-scrollbar`}>\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl shadow-lg ${\n                message.sender === 'me'\n                  ? darkMode \n                    ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'\n                    : 'bg-gradient-to-r from-pink-500 to-rose-500 text-white'\n                  : darkMode\n                    ? 'bg-gray-700 text-gray-100 border border-gray-600'\n                    : 'bg-white text-gray-800 border border-gray-200'\n              } relative group`}>\n                {editingMessage === message.id ? (\n                  <div className=\"space-y-2\">\n                    <textarea\n                      value={editContent}\n                      onChange={(e) => setEditContent(e.target.value)}\n                      onKeyPress={handleEditKeyPress}\n                      className={`w-full p-2 rounded-lg resize-none ${\n                        darkMode ? 'bg-gray-600 text-white' : 'bg-gray-100 text-gray-800'\n                      }`}\n                      rows={2}\n                    />\n                    <div className=\"flex space-x-2\">\n                      <button\n                        onClick={handleSaveEdit}\n                        className=\"px-3 py-1 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600\"\n                      >\n                        <Save size={14} />\n                      </button>\n                      <button\n                        onClick={handleCancelEdit}\n                        className=\"px-3 py-1 bg-gray-500 text-white rounded-lg text-sm hover:bg-gray-600\"\n                      >\n                        <X size={14} />\n                      </button>\n                    </div>\n                  </div>\n                ) : (\n                  <>\n                    <p className=\"text-sm leading-relaxed\">{message.text}</p>\n                    <div className=\"flex items-center justify-between mt-2\">\n                      <span className={`text-xs ${\n                        message.sender === 'me' \n                          ? 'text-white/70' \n                          : darkMode ? 'text-gray-400' : 'text-gray-500'\n                      }`}>\n                        {formatTime(message.timestamp)}\n                      </span>\n                    </div>\n                  </>\n                )}\n              </div>\n            </div>\n          ))}\n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Message Input */}\n        <div className={`${\n          darkMode ? 'bg-gray-800/90 border-gray-700' : 'bg-white/90 border-pink-200'\n        } backdrop-blur-sm rounded-b-2xl p-4 border-t`}>\n          <form onSubmit={sendMessage} className=\"flex items-end space-x-3\">\n            <div className=\"flex-1 relative\">\n              <textarea\n                value={newMessage}\n                onChange={(e) => setNewMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"Type your love message... 💕\"\n                rows={1}\n                className={`w-full px-4 py-3 rounded-xl resize-none focus:outline-none focus:ring-2 ${\n                  darkMode \n                    ? 'bg-gray-700 text-white border-gray-600 focus:ring-purple-500 placeholder-gray-400'\n                    : 'bg-pink-50 text-gray-800 border-pink-200 focus:ring-pink-300 placeholder-pink-400'\n                } border-2 transition-all duration-300`}\n              />\n            </div>\n            <button\n              type=\"submit\"\n              disabled={!newMessage.trim()}\n              className={`px-6 py-3 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2 ${\n                darkMode\n                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'\n                  : 'bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600'\n              } text-white disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl hover:scale-105`}\n            >\n              <Send size={18} />\n            </button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Chat;\n\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjH,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI,EAAEC;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACpC,MAAM;IAAEmB;EAAS,CAAC,GAAGlB,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,CACvC;IACEyB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,6BAA6B;IACnCC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE,CAAC,IAAI;EAClB,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,4CAA4C;IAClDC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC;IAAEyB,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAE;EAAQ,CAAC,CAAC;EACpE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC;IAAEyB,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAE;EAAO,CAAC,CAAC;EACvE,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,MAAM,EAAEC,SAAS,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC6C,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM+C,cAAc,GAAG7C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM8C,gBAAgB,GAAG9C,MAAM,CAAC,IAAI,CAAC;EAErC,MAAM+C,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;EAE9G;EACA,MAAM7B,IAAI,GAAGC,QAAQ;;EAErB;EACA,MAAM6B,aAAa,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,CAAC,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAA6C,CAAC;MAC7E,CAAC,EAAE;QAAED,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAwC;IACzE,CAAC;IACD,OAAOF,OAAO,CAACF,MAAM,CAAC,IAAI;MAAEG,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAA4B,CAAC;EAClF,CAAC;EAEDtD,SAAS,CAAC,MAAM;IACduD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;EAEdtB,SAAS,CAAC,MAAM;IACd;IACA,MAAMwD,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7Bd,kBAAkB,CAAC,IAAI,CAAC;MACxBc,UAAU,CAAC,MAAMd,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACnD,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMe,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAAC,CAAC,IAAI,CAAC7B,SAAS,EAAE;IAEtC,MAAM8B,MAAM,GAAG;MACbvC,EAAE,EAAEF,QAAQ,CAAC0C,MAAM,GAAG,CAAC;MACvBC,SAAS,EAAE9C,IAAI,CAACK,EAAE;MAClB0C,WAAW,EAAE/C,IAAI,CAACgB,IAAI;MACtBgC,WAAW,EAAElC,SAAS,CAACT,EAAE;MACzB4C,aAAa,EAAEnC,SAAS,CAACE,IAAI;MAC7BkC,OAAO,EAAEtC,UAAU,CAAC+B,IAAI,CAAC,CAAC;MAC1BQ,UAAU,EAAE,IAAI1C,IAAI,CAAC;IACvB,CAAC;IAEDL,WAAW,CAACgD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,MAAM,CAAC,CAAC;IACtC/B,aAAa,CAAC,EAAE,CAAC;IACjBa,kBAAkB,CAAC,KAAK,CAAC;IACzBJ,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAM+B,YAAY,GAAIZ,CAAC,IAAK;IAC1B5B,aAAa,CAAC4B,CAAC,CAACa,MAAM,CAACC,KAAK,CAAC;IAE7B,IAAI,CAAClC,MAAM,EAAE;MACXC,SAAS,CAAC,IAAI,CAAC;IACjB;IAEAiB,YAAY,CAACX,gBAAgB,CAAC4B,OAAO,CAAC;IACtC5B,gBAAgB,CAAC4B,OAAO,GAAGlB,UAAU,CAAC,MAAM;MAC1ChB,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMmC,QAAQ,GAAIvB,KAAK,IAAK;IAC1BrB,aAAa,CAACuC,IAAI,IAAIA,IAAI,GAAGlB,KAAK,CAAC;IACnCR,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAsB,qBAAA;IAC3B,CAAAA,qBAAA,GAAA/B,cAAc,CAAC6B,OAAO,cAAAE,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,cAAc,GAAIpB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACqB,GAAG,KAAK,OAAO,IAAI,CAACrB,CAAC,CAACsB,QAAQ,EAAE;MACpCtB,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBF,WAAW,CAACC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMuB,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIxD,IAAI,CAACwD,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACtF,CAAC;EAED,IAAIjD,OAAO,EAAE;IACX,oBACExB,OAAA;MAAK0E,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChH3E,OAAA;QAAK0E,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B3E,OAAA;UAAK0E,SAAS,EAAC;QAA6F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnH/E,OAAA;UAAG0E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/E,OAAA;IAAK0E,SAAS,EAAE,+CACdnE,QAAQ,GACJ,8DAA8D,GAC9D,yDAAyD,EAC5D;IAAAoE,QAAA,eACD3E,OAAA;MAAK0E,SAAS,EAAC,8CAA8C;MAAAC,QAAA,gBAE3D3E,OAAA;QAAK0E,SAAS,EAAE,GACdnE,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,wDACpB;QAAAoE,QAAA,eACvD3E,OAAA;UAAK0E,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChD3E,OAAA;YAAK0E,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C3E,OAAA;cAAK0E,SAAS,EAAC,6HAA6H;cAAAC,QAAA,EAAC;YAE7I;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN/E,OAAA;cAAA2E,QAAA,gBACE3E,OAAA;gBAAI0E,SAAS,EAAE,qBAAqBnE,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;gBAAAoE,QAAA,EAC7ExD,SAAS,CAACE;cAAI;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACL/E,OAAA;gBAAG0E,SAAS,EAAE,WAAWnE,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAAoE,QAAA,EACrE/C,eAAe,GAAG,WAAW,GAAG;cAAQ;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/E,OAAA;QAAK0E,SAAS,EAAE,UACdnE,QAAQ,GAAG,gBAAgB,GAAG,aAAa,kEACsB;QAAAoE,QAAA,GAChEnE,QAAQ,CAACwE,GAAG,CAAEzB,OAAO,iBACpBvD,OAAA;UAEE0E,SAAS,EAAE,QAAQnB,OAAO,CAAC3C,MAAM,KAAK,IAAI,GAAG,aAAa,GAAG,eAAe,EAAG;UAAA+D,QAAA,eAE/E3E,OAAA;YAAK0E,SAAS,EAAE,wDACdnB,OAAO,CAAC3C,MAAM,KAAK,IAAI,GACnBL,QAAQ,GACN,yDAAyD,GACzD,uDAAuD,GACzDA,QAAQ,GACN,kDAAkD,GAClD,+CAA+C,iBACrC;YAAAoE,QAAA,EACfM,cAAc,KAAK1B,OAAO,CAAC7C,EAAE,gBAC5BV,OAAA;cAAK0E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB3E,OAAA;gBACE4D,KAAK,EAAEsB,WAAY;gBACnBC,QAAQ,EAAGrC,CAAC,IAAKsC,cAAc,CAACtC,CAAC,CAACa,MAAM,CAACC,KAAK,CAAE;gBAChDyB,UAAU,EAAEC,kBAAmB;gBAC/BZ,SAAS,EAAE,qCACTnE,QAAQ,GAAG,wBAAwB,GAAG,2BAA2B,EAChE;gBACHgF,IAAI,EAAE;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACF/E,OAAA;gBAAK0E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7B3E,OAAA;kBACEwF,OAAO,EAAEC,cAAe;kBACxBf,SAAS,EAAC,yEAAyE;kBAAAC,QAAA,eAEnF3E,OAAA,CAAC0F,IAAI;oBAACC,IAAI,EAAE;kBAAG;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACT/E,OAAA;kBACEwF,OAAO,EAAEI,gBAAiB;kBAC1BlB,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,eAEjF3E,OAAA,CAAC6F,CAAC;oBAACF,IAAI,EAAE;kBAAG;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN/E,OAAA,CAAAE,SAAA;cAAAyE,QAAA,gBACE3E,OAAA;gBAAG0E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAEpB,OAAO,CAAC5C;cAAI;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzD/E,OAAA;gBAAK0E,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrD3E,OAAA;kBAAM0E,SAAS,EAAE,WACfnB,OAAO,CAAC3C,MAAM,KAAK,IAAI,GACnB,eAAe,GACfL,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC/C;kBAAAoE,QAAA,EACAN,UAAU,CAACd,OAAO,CAAC1C,SAAS;gBAAC;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,eACN;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GApDDxB,OAAO,CAAC7C,EAAE;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqDZ,CACN,CAAC,eACF/E,OAAA;UAAK8F,GAAG,EAAE9D;QAAe;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAGN/E,OAAA;QAAK0E,SAAS,EAAE,GACdnE,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,8CAC9B;QAAAoE,QAAA,eAC7C3E,OAAA;UAAM+F,QAAQ,EAAElD,WAAY;UAAC6B,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBAC/D3E,OAAA;YAAK0E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9B3E,OAAA;cACE4D,KAAK,EAAE3C,UAAW;cAClBkE,QAAQ,EAAGrC,CAAC,IAAK5B,aAAa,CAAC4B,CAAC,CAACa,MAAM,CAACC,KAAK,CAAE;cAC/CyB,UAAU,EAAEnB,cAAe;cAC3B8B,WAAW,EAAC,wCAA8B;cAC1CT,IAAI,EAAE,CAAE;cACRb,SAAS,EAAE,2EACTnE,QAAQ,GACJ,mFAAmF,GACnF,mFAAmF;YACjD;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN/E,OAAA;YACEiG,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAE,CAACjF,UAAU,CAAC+B,IAAI,CAAC,CAAE;YAC7B0B,SAAS,EAAE,4FACTnE,QAAQ,GACJ,sFAAsF,GACtF,kFAAkF,uGACgB;YAAAoE,QAAA,eAExG3E,OAAA,CAACV,IAAI;cAACqG,IAAI,EAAE;YAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAxPID,IAAI;EAAA,QACmBf,OAAO,EACbC,QAAQ;AAAA;AAAA8G,EAAA,GAFzBhG,IAAI;AA0PV,eAAeA,IAAI;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
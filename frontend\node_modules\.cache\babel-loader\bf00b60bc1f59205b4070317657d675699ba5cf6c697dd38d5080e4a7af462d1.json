{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Chat.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Heart, Smile, Image, Paperclip, MoreVertical, Star, Sparkles, MessageCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Chat = () => {\n  _s();\n  const {\n    user: authUser\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n  const [messages, setMessages] = useState([{\n    id: 1,\n    text: \"Good morning, beautiful! ☀️\",\n    sender: 'other',\n    timestamp: new Date(Date.now() - 3600000),\n    reactions: ['❤️']\n  }, {\n    id: 2,\n    text: \"Good morning, love! Hope you slept well 💕\",\n    sender: 'me',\n    timestamp: new Date(Date.now() - 3500000),\n    reactions: []\n  }]);\n  const [newMessage, setNewMessage] = useState('');\n  const [otherUser, setOtherUser] = useState({\n    id: 2,\n    name: 'Sarah'\n  });\n  const [currentUser, setCurrentUser] = useState({\n    id: 1,\n    name: 'Alex'\n  });\n  const [loading, setLoading] = useState(false);\n  const [typing, setTyping] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n  const emojis = ['❤️', '💕', '💖', '💗', '🥰', '😘', '😍', '🌹', '✨', '💫', '🌟', '💎', '🎉', '🔥', '💯', '👑'];\n\n  // Use authUser instead of user\n  const user = authUser;\n\n  // Enhanced user avatars with gradients\n  const getUserAvatar = (userId, userName) => {\n    const avatars = {\n      1: {\n        emoji: '👨‍💼',\n        gradient: 'from-blue-500 via-purple-500 to-indigo-600'\n      },\n      2: {\n        emoji: '👩‍💼',\n        gradient: 'from-pink-500 via-rose-500 to-red-500'\n      }\n    };\n    return avatars[userId] || {\n      emoji: '😊',\n      gradient: 'from-gray-400 to-gray-600'\n    };\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  useEffect(() => {\n    // Simulate typing indicator\n    const timer = setTimeout(() => {\n      setOtherUserTyping(true);\n      setTimeout(() => setOtherUserTyping(false), 3000);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n  const sendMessage = async e => {\n    e.preventDefault();\n    if (!newMessage.trim() || !otherUser) return;\n    const newMsg = {\n      id: messages.length + 1,\n      sender_id: user.id,\n      sender_name: user.name,\n      receiver_id: otherUser.id,\n      receiver_name: otherUser.name,\n      message: newMessage.trim(),\n      created_at: new Date()\n    };\n    setMessages(prev => [...prev, newMsg]);\n    setNewMessage('');\n    setShowEmojiPicker(false);\n    setTyping(false);\n  };\n  const handleTyping = e => {\n    setNewMessage(e.target.value);\n    if (!typing) {\n      setTyping(true);\n    }\n    clearTimeout(typingTimeoutRef.current);\n    typingTimeoutRef.current = setTimeout(() => {\n      setTyping(false);\n    }, 1000);\n  };\n  const addEmoji = emoji => {\n    setNewMessage(prev => prev + emoji);\n    setShowEmojiPicker(false);\n  };\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(e);\n    }\n  };\n  const formatTime = date => {\n    return new Date(date).toLocaleTimeString([], {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 text-lg font-medium\",\n          children: \"Loading your conversation...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 p-4 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [...Array(20)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute animate-float opacity-20\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 5}s`,\n          animationDuration: `${3 + Math.random() * 4}s`\n        },\n        children: i % 4 === 0 ? '💕' : i % 4 === 1 ? '✨' : i % 4 === 2 ? '🌟' : '💫'\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto relative\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"backdrop-blur-xl bg-white/30 rounded-3xl shadow-2xl border border-white/20 h-[90vh] flex flex-col overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative px-8 py-6 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-sm border-b border-white/10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-16 h-16 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).gradient} flex items-center justify-center text-white text-2xl shadow-xl transform transition-all duration-300 hover:scale-105`,\n                  children: getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).emoji\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-4 border-white shadow-lg animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-gray-800 mb-1\",\n                  children: (otherUser === null || otherUser === void 0 ? void 0 : otherUser.name) || 'Your Partner'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600 flex items-center\",\n                  children: otherUserTyping ? /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center text-green-600 font-medium\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex space-x-1 mr-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full animate-bounce\",\n                        style: {\n                          animationDelay: '0s'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 167,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full animate-bounce\",\n                        style: {\n                          animationDelay: '0.1s'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 168,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-green-500 rounded-full animate-bounce\",\n                        style: {\n                          animationDelay: '0.2s'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 169,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 25\n                    }, this), \"Typing...\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 23\n                  }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"flex items-center text-green-600\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 175,\n                      columnNumber: 25\n                    }, this), \"Active now\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 174,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl animate-pulse\",\n                children: \"\\uD83D\\uDC96\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Star, {\n                  className: \"w-6 h-6 text-yellow-500 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-6 h-6 text-purple-500 animate-pulse\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform skew-x-12 translate-x-full animate-shimmer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 overflow-y-auto px-8 py-6 space-y-6 relative\",\n          children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-gray-500 py-20\",\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {\n              className: \"w-20 h-20 mx-auto mb-6 text-gray-300 animate-bounce\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl font-semibold mb-2\",\n              children: \"No messages yet\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-400\",\n              children: \"Start your conversation with something sweet!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this) : messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex ${message.sender_id === user.id ? 'justify-end' : 'justify-start'} transform transition-all duration-500 animate-fadeInUp`,\n            style: {\n              animationDelay: `${index * 0.1}s`\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-end space-x-3 max-w-lg ${message.sender_id === user.id ? 'flex-row-reverse space-x-reverse' : ''}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(message.sender_id, message.sender_name).gradient} flex items-center justify-center text-white text-lg shadow-lg transform transition-all duration-300 hover:scale-110`,\n                children: getUserAvatar(message.sender_id, message.sender_name).emoji\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative group\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-6 py-4 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 ${message.sender_id === user.id ? 'bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white rounded-br-md shadow-purple-200' : 'bg-gradient-to-br from-white to-gray-50 text-gray-800 rounded-bl-md shadow-gray-200 border border-white/50'}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm leading-relaxed font-medium\",\n                    children: message.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: `text-xs mt-2 ${message.sender_id === user.id ? 'text-white/70' : 'text-gray-500'}`,\n                    children: formatTime(message.created_at)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-2xl transform scale-x-0 group-hover:scale-x-100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute ${message.sender_id === user.id ? '-right-2 bottom-4' : '-left-2 bottom-4'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-4 h-4 transform rotate-45 ${message.sender_id === user.id ? 'bg-gradient-to-br from-purple-500 to-rose-500' : 'bg-gradient-to-br from-white to-gray-50 border-l border-b border-white/50'}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 19\n            }, this)\n          }, message.id || index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this)), otherUserTyping && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-start animate-fadeIn\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).gradient} flex items-center justify-center text-white text-lg shadow-lg animate-pulse`,\n                children: getUserAvatar(otherUser === null || otherUser === void 0 ? void 0 : otherUser.id, otherUser === null || otherUser === void 0 ? void 0 : otherUser.name).emoji\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-gray-100 to-white px-6 py-4 rounded-2xl rounded-bl-md shadow-xl border border-gray-200\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.2s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-3 h-3 bg-gray-400 rounded-full animate-bounce\",\n                    style: {\n                      animationDelay: '0.4s'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-8 py-6 bg-gradient-to-r from-white/40 to-white/20 backdrop-blur-sm border-t border-white/10 relative\",\n          children: [showEmojiPicker && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-full left-8 right-8 mb-4 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 animate-slideUp\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-8 gap-3\",\n              children: emojis.map((emoji, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => addEmoji(emoji),\n                className: \"text-2xl p-3 rounded-xl hover:bg-pink-100/70 transition-all duration-200 transform hover:scale-125 hover:rotate-12\",\n                style: {\n                  animationDelay: `${index * 0.05}s`\n                },\n                children: emoji\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-end space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setShowEmojiPicker(!showEmojiPicker),\n                className: \"w-12 h-12 rounded-2xl bg-gradient-to-br from-yellow-400 to-orange-500 text-white flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12\",\n                children: /*#__PURE__*/_jsxDEV(Smile, {\n                  className: \"w-6 h-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1 relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: newMessage,\n                  onChange: handleTyping,\n                  onKeyPress: handleKeyPress,\n                  placeholder: \"Type something sweet...\",\n                  rows: 1,\n                  className: \"w-full px-6 py-4 bg-white/70 backdrop-blur-sm border-2 border-white/30 rounded-2xl focus:outline-none focus:ring-4 focus:ring-purple-200/50 focus:border-purple-400/50 resize-none transition-all duration-300 text-gray-800 placeholder-gray-500 shadow-inner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-400/10 via-pink-400/10 to-rose-400/10 opacity-0 hover:opacity-100 transition-all duration-300 pointer-events-none\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: sendMessage,\n                disabled: !newMessage.trim(),\n                className: \"w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white flex items-center justify-center shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-110 hover:shadow-xl relative overflow-hidden group\",\n                children: [/*#__PURE__*/_jsxDEV(Send, {\n                  className: \"w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform translate-x-full group-hover:translate-x-0 transition-transform duration-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-xs text-gray-500\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center space-x-2\",\n                children: [/*#__PURE__*/_jsxDEV(Heart, {\n                  className: \"w-3 h-3 text-pink-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Press Enter to send \\u2022 Shift+Enter for new line\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"w-3 h-3 text-purple-400\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [newMessage.length, \"/500\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-10px) rotate(5deg); }\n        }\n        \n        @keyframes fadeInUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0); \n          }\n        }\n        \n        @keyframes slideUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px) scale(0.95); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0) scale(1); \n          }\n        }\n        \n        @keyframes shimmer {\n          0% { transform: translateX(-100%) skewX(12deg); }\n          100% { transform: translateX(200%) skewX(12deg); }\n        }\n        \n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n        \n        .animate-fadeInUp {\n          animation: fadeInUp 0.6s ease-out forwards;\n        }\n        \n        .animate-slideUp {\n          animation: slideUp 0.3s ease-out forwards;\n        }\n        \n        .animate-shimmer {\n          animation: shimmer 3s infinite;\n        }\n        \n        .animate-fadeIn {\n          animation: fadeInUp 0.3s ease-out forwards;\n        }\n        \n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 8px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: rgba(255, 255, 255, 0.1);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: linear-gradient(to bottom, #ec4899, #be185d);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: linear-gradient(to bottom, #db2777, #9d174d);\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 334,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 128,\n    columnNumber: 5\n  }, this);\n};\n_s(Chat, \"Mk7QZ4jUQdYA+IofGoP0HC+Unbc=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Chat;\nexport default Chat;\nvar _c;\n$RefreshReg$(_c, \"Chat\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useAuth", "useTheme", "Send", "Heart", "Smile", "Image", "Paperclip", "MoreVertical", "Star", "<PERSON><PERSON><PERSON>", "MessageCircle", "jsxDEV", "_jsxDEV", "Cha<PERSON>", "_s", "user", "authUser", "darkMode", "messages", "setMessages", "id", "text", "sender", "timestamp", "Date", "now", "reactions", "newMessage", "setNewMessage", "otherUser", "setOtherUser", "name", "currentUser", "setCurrentUser", "loading", "setLoading", "typing", "setTyping", "otherUserTyping", "setOtherUserTyping", "showEmojiPicker", "setShowEmojiPicker", "messagesEndRef", "typingTimeoutRef", "emojis", "getUserAvatar", "userId", "userName", "avatars", "emoji", "gradient", "scrollToBottom", "timer", "setTimeout", "clearTimeout", "sendMessage", "e", "preventDefault", "trim", "newMsg", "length", "sender_id", "sender_name", "receiver_id", "receiver_name", "message", "created_at", "prev", "handleTyping", "target", "value", "current", "addEmoji", "_messagesEndRef$curre", "scrollIntoView", "behavior", "handleKeyPress", "key", "shift<PERSON>ey", "formatTime", "date", "toLocaleTimeString", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "index", "ref", "onClick", "onChange", "onKeyPress", "placeholder", "rows", "disabled", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Chat.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Send, Heart, Smile, Image, Paperclip, MoreVertical, Star, Sparkles, MessageCircle } from 'lucide-react';\n\nconst Chat = () => {\n  const { user: authUser } = useAuth();\n  const { darkMode } = useTheme();\n  const [messages, setMessages] = useState([\n    {\n      id: 1,\n      text: \"Good morning, beautiful! ☀️\",\n      sender: 'other',\n      timestamp: new Date(Date.now() - 3600000),\n      reactions: ['❤️']\n    },\n    {\n      id: 2,\n      text: \"Good morning, love! Hope you slept well 💕\",\n      sender: 'me',\n      timestamp: new Date(Date.now() - 3500000),\n      reactions: []\n    }\n  ]);\n  \n  const [newMessage, setNewMessage] = useState('');\n  const [otherUser, setOtherUser] = useState({ id: 2, name: '<PERSON>' });\n  const [currentUser, setCurrentUser] = useState({ id: 1, name: '<PERSON>' });\n  const [loading, setLoading] = useState(false);\n  const [typing, setTyping] = useState(false);\n  const [otherUserTyping, setOtherUserTyping] = useState(false);\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\n  const messagesEndRef = useRef(null);\n  const typingTimeoutRef = useRef(null);\n\n  const emojis = ['❤️', '💕', '💖', '💗', '🥰', '😘', '😍', '🌹', '✨', '💫', '🌟', '💎', '🎉', '🔥', '💯', '👑'];\n\n  // Use authUser instead of user\n  const user = authUser;\n\n  // Enhanced user avatars with gradients\n  const getUserAvatar = (userId, userName) => {\n    const avatars = {\n      1: { emoji: '👨‍💼', gradient: 'from-blue-500 via-purple-500 to-indigo-600' },\n      2: { emoji: '👩‍💼', gradient: 'from-pink-500 via-rose-500 to-red-500' }\n    };\n    return avatars[userId] || { emoji: '😊', gradient: 'from-gray-400 to-gray-600' };\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    // Simulate typing indicator\n    const timer = setTimeout(() => {\n      setOtherUserTyping(true);\n      setTimeout(() => setOtherUserTyping(false), 3000);\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const sendMessage = async (e) => {\n    e.preventDefault();\n    if (!newMessage.trim() || !otherUser) return;\n\n    const newMsg = {\n      id: messages.length + 1,\n      sender_id: user.id,\n      sender_name: user.name,\n      receiver_id: otherUser.id,\n      receiver_name: otherUser.name,\n      message: newMessage.trim(),\n      created_at: new Date()\n    };\n\n    setMessages(prev => [...prev, newMsg]);\n    setNewMessage('');\n    setShowEmojiPicker(false);\n    setTyping(false);\n  };\n\n  const handleTyping = (e) => {\n    setNewMessage(e.target.value);\n    \n    if (!typing) {\n      setTyping(true);\n    }\n\n    clearTimeout(typingTimeoutRef.current);\n    typingTimeoutRef.current = setTimeout(() => {\n      setTyping(false);\n    }, 1000);\n  };\n\n  const addEmoji = (emoji) => {\n    setNewMessage(prev => prev + emoji);\n    setShowEmojiPicker(false);\n  };\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(e);\n    }\n  };\n\n  const formatTime = (date) => {\n    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-pink-200 border-t-pink-500 rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 text-lg font-medium\">Loading your conversation...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 p-4 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(20)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute animate-float opacity-20\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 5}s`,\n              animationDuration: `${3 + Math.random() * 4}s`\n            }}\n          >\n            {i % 4 === 0 ? '💕' : i % 4 === 1 ? '✨' : i % 4 === 2 ? '🌟' : '💫'}\n          </div>\n        ))}\n      </div>\n\n      <div className=\"max-w-4xl mx-auto relative\">\n        <div className=\"backdrop-blur-xl bg-white/30 rounded-3xl shadow-2xl border border-white/20 h-[90vh] flex flex-col overflow-hidden\">\n          {/* Enhanced Header */}\n          <div className=\"relative px-8 py-6 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-indigo-500/20 backdrop-blur-sm border-b border-white/10\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"relative\">\n                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser?.id, otherUser?.name).gradient} flex items-center justify-center text-white text-2xl shadow-xl transform transition-all duration-300 hover:scale-105`}>\n                    {getUserAvatar(otherUser?.id, otherUser?.name).emoji}\n                  </div>\n                  <div className=\"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-4 border-white shadow-lg animate-pulse\"></div>\n                </div>\n                <div>\n                  <h3 className=\"text-2xl font-bold text-gray-800 mb-1\">\n                    {otherUser?.name || 'Your Partner'}\n                  </h3>\n                  <p className=\"text-sm text-gray-600 flex items-center\">\n                    {otherUserTyping ? (\n                      <span className=\"flex items-center text-green-600 font-medium\">\n                        <div className=\"flex space-x-1 mr-2\">\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-bounce\" style={{animationDelay: '0.1s'}}></div>\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                        </div>\n                        Typing...\n                      </span>\n                    ) : (\n                      <span className=\"flex items-center text-green-600\">\n                        <div className=\"w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse\"></div>\n                        Active now\n                      </span>\n                    )}\n                  </p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-4xl animate-pulse\">💖</div>\n                <div className=\"flex space-x-2\">\n                  <Star className=\"w-6 h-6 text-yellow-500 animate-pulse\" />\n                  <Sparkles className=\"w-6 h-6 text-purple-500 animate-pulse\" />\n                </div>\n              </div>\n            </div>\n\n            {/* Animated background pattern */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform skew-x-12 translate-x-full animate-shimmer\"></div>\n          </div>\n\n          {/* Messages Container */}\n          <div className=\"flex-1 overflow-y-auto px-8 py-6 space-y-6 relative\">\n            {messages.length === 0 ? (\n              <div className=\"text-center text-gray-500 py-20\">\n                <MessageCircle className=\"w-20 h-20 mx-auto mb-6 text-gray-300 animate-bounce\" />\n                <p className=\"text-xl font-semibold mb-2\">No messages yet</p>\n                <p className=\"text-gray-400\">Start your conversation with something sweet!</p>\n              </div>\n            ) : (\n              messages.map((message, index) => (\n                <div\n                  key={message.id || index}\n                  className={`flex ${message.sender_id === user.id ? 'justify-end' : 'justify-start'} transform transition-all duration-500 animate-fadeInUp`}\n                  style={{ animationDelay: `${index * 0.1}s` }}\n                >\n                  <div className={`flex items-end space-x-3 max-w-lg ${message.sender_id === user.id ? 'flex-row-reverse space-x-reverse' : ''}`}>\n                    <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(message.sender_id, message.sender_name).gradient} flex items-center justify-center text-white text-lg shadow-lg transform transition-all duration-300 hover:scale-110`}>\n                      {getUserAvatar(message.sender_id, message.sender_name).emoji}\n                    </div>\n                    \n                    <div className=\"relative group\">\n                      <div\n                        className={`px-6 py-4 rounded-2xl shadow-xl transform transition-all duration-300 hover:scale-105 ${\n                          message.sender_id === user.id\n                            ? 'bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white rounded-br-md shadow-purple-200'\n                            : 'bg-gradient-to-br from-white to-gray-50 text-gray-800 rounded-bl-md shadow-gray-200 border border-white/50'\n                        }`}\n                      >\n                        <p className=\"text-sm leading-relaxed font-medium\">{message.message}</p>\n                        <p className={`text-xs mt-2 ${message.sender_id === user.id ? 'text-white/70' : 'text-gray-500'}`}>\n                          {formatTime(message.created_at)}\n                        </p>\n                        \n                        {/* Message hover effect */}\n                        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 rounded-2xl transform scale-x-0 group-hover:scale-x-100\"></div>\n                      </div>\n                      \n                      {/* Message tail */}\n                      <div className={`absolute ${message.sender_id === user.id ? '-right-2 bottom-4' : '-left-2 bottom-4'}`}>\n                        <div className={`w-4 h-4 transform rotate-45 ${\n                          message.sender_id === user.id\n                            ? 'bg-gradient-to-br from-purple-500 to-rose-500'\n                            : 'bg-gradient-to-br from-white to-gray-50 border-l border-b border-white/50'\n                        }`}></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))\n            )}\n            \n            {/* Enhanced typing indicator */}\n            {otherUserTyping && (\n              <div className=\"flex justify-start animate-fadeIn\">\n                <div className=\"flex items-end space-x-3\">\n                  <div className={`w-12 h-12 rounded-2xl bg-gradient-to-br ${getUserAvatar(otherUser?.id, otherUser?.name).gradient} flex items-center justify-center text-white text-lg shadow-lg animate-pulse`}>\n                    {getUserAvatar(otherUser?.id, otherUser?.name).emoji}\n                  </div>\n                  <div className=\"bg-gradient-to-r from-gray-100 to-white px-6 py-4 rounded-2xl rounded-bl-md shadow-xl border border-gray-200\">\n                    <div className=\"flex space-x-2\">\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0s'}}></div>\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.2s'}}></div>\n                      <div className=\"w-3 h-3 bg-gray-400 rounded-full animate-bounce\" style={{animationDelay: '0.4s'}}></div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n            \n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* Enhanced Message Input */}\n          <div className=\"px-8 py-6 bg-gradient-to-r from-white/40 to-white/20 backdrop-blur-sm border-t border-white/10 relative\">\n            {/* Emoji Picker */}\n            {showEmojiPicker && (\n              <div className=\"absolute bottom-full left-8 right-8 mb-4 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6 animate-slideUp\">\n                <div className=\"grid grid-cols-8 gap-3\">\n                  {emojis.map((emoji, index) => (\n                    <button\n                      key={index}\n                      onClick={() => addEmoji(emoji)}\n                      className=\"text-2xl p-3 rounded-xl hover:bg-pink-100/70 transition-all duration-200 transform hover:scale-125 hover:rotate-12\"\n                      style={{ animationDelay: `${index * 0.05}s` }}\n                    >\n                      {emoji}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-end space-x-4\">\n                <button\n                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                  className=\"w-12 h-12 rounded-2xl bg-gradient-to-br from-yellow-400 to-orange-500 text-white flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 hover:rotate-12\"\n                >\n                  <Smile className=\"w-6 h-6\" />\n                </button>\n                \n                <div className=\"flex-1 relative\">\n                  <textarea\n                    value={newMessage}\n                    onChange={handleTyping}\n                    onKeyPress={handleKeyPress}\n                    placeholder=\"Type something sweet...\"\n                    rows={1}\n                    className=\"w-full px-6 py-4 bg-white/70 backdrop-blur-sm border-2 border-white/30 rounded-2xl focus:outline-none focus:ring-4 focus:ring-purple-200/50 focus:border-purple-400/50 resize-none transition-all duration-300 text-gray-800 placeholder-gray-500 shadow-inner\"\n                  />\n                  <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-400/10 via-pink-400/10 to-rose-400/10 opacity-0 hover:opacity-100 transition-all duration-300 pointer-events-none\"></div>\n                </div>\n                \n                <button\n                  onClick={sendMessage}\n                  disabled={!newMessage.trim()}\n                  className=\"w-12 h-12 rounded-2xl bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 text-white flex items-center justify-center shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-110 hover:shadow-xl relative overflow-hidden group\"\n                >\n                  <Send className=\"w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200\" />\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent transform translate-x-full group-hover:translate-x-0 transition-transform duration-500\"></div>\n                </button>\n              </div>\n              \n              <div className=\"flex items-center justify-between text-xs text-gray-500\">\n                <span className=\"flex items-center space-x-2\">\n                  <Heart className=\"w-3 h-3 text-pink-400\" />\n                  <span>Press Enter to send • Shift+Enter for new line</span>\n                </span>\n                <span className=\"flex items-center space-x-1\">\n                  <Sparkles className=\"w-3 h-3 text-purple-400\" />\n                  <span>{newMessage.length}/500</span>\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          50% { transform: translateY(-10px) rotate(5deg); }\n        }\n        \n        @keyframes fadeInUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0); \n          }\n        }\n        \n        @keyframes slideUp {\n          from { \n            opacity: 0; \n            transform: translateY(20px) scale(0.95); \n          }\n          to { \n            opacity: 1; \n            transform: translateY(0) scale(1); \n          }\n        }\n        \n        @keyframes shimmer {\n          0% { transform: translateX(-100%) skewX(12deg); }\n          100% { transform: translateX(200%) skewX(12deg); }\n        }\n        \n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n        \n        .animate-fadeInUp {\n          animation: fadeInUp 0.6s ease-out forwards;\n        }\n        \n        .animate-slideUp {\n          animation: slideUp 0.3s ease-out forwards;\n        }\n        \n        .animate-shimmer {\n          animation: shimmer 3s infinite;\n        }\n        \n        .animate-fadeIn {\n          animation: fadeInUp 0.3s ease-out forwards;\n        }\n        \n        /* Custom scrollbar */\n        ::-webkit-scrollbar {\n          width: 8px;\n        }\n        \n        ::-webkit-scrollbar-track {\n          background: rgba(255, 255, 255, 0.1);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb {\n          background: linear-gradient(to bottom, #ec4899, #be185d);\n          border-radius: 10px;\n        }\n        \n        ::-webkit-scrollbar-thumb:hover {\n          background: linear-gradient(to bottom, #db2777, #9d174d);\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default Chat;\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,YAAY,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjH,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC,IAAI,EAAEC;EAAS,CAAC,GAAGhB,OAAO,CAAC,CAAC;EACpC,MAAM;IAAEiB;EAAS,CAAC,GAAGhB,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACiB,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CACvC;IACEuB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,6BAA6B;IACnCC,MAAM,EAAE,OAAO;IACfC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE,CAAC,IAAI;EAClB,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,4CAA4C;IAClDC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;IACzCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACgC,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC;IAAEuB,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAE;EAAQ,CAAC,CAAC;EACpE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC;IAAEuB,EAAE,EAAE,CAAC;IAAEW,IAAI,EAAE;EAAO,CAAC,CAAC;EACvE,MAAM,CAACG,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyC,eAAe,EAAEC,kBAAkB,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM6C,cAAc,GAAG3C,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM4C,gBAAgB,GAAG5C,MAAM,CAAC,IAAI,CAAC;EAErC,MAAM6C,MAAM,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;;EAE9G;EACA,MAAM7B,IAAI,GAAGC,QAAQ;;EAErB;EACA,MAAM6B,aAAa,GAAGA,CAACC,MAAM,EAAEC,QAAQ,KAAK;IAC1C,MAAMC,OAAO,GAAG;MACd,CAAC,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAA6C,CAAC;MAC7E,CAAC,EAAE;QAAED,KAAK,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAwC;IACzE,CAAC;IACD,OAAOF,OAAO,CAACF,MAAM,CAAC,IAAI;MAAEG,KAAK,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAA4B,CAAC;EAClF,CAAC;EAEDpD,SAAS,CAAC,MAAM;IACdqD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;EAEdpB,SAAS,CAAC,MAAM;IACd;IACA,MAAMsD,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7Bd,kBAAkB,CAAC,IAAI,CAAC;MACxBc,UAAU,CAAC,MAAMd,kBAAkB,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;IACnD,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMe,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,WAAW,GAAG,MAAOC,CAAC,IAAK;IAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC9B,UAAU,CAAC+B,IAAI,CAAC,CAAC,IAAI,CAAC7B,SAAS,EAAE;IAEtC,MAAM8B,MAAM,GAAG;MACbvC,EAAE,EAAEF,QAAQ,CAAC0C,MAAM,GAAG,CAAC;MACvBC,SAAS,EAAE9C,IAAI,CAACK,EAAE;MAClB0C,WAAW,EAAE/C,IAAI,CAACgB,IAAI;MACtBgC,WAAW,EAAElC,SAAS,CAACT,EAAE;MACzB4C,aAAa,EAAEnC,SAAS,CAACE,IAAI;MAC7BkC,OAAO,EAAEtC,UAAU,CAAC+B,IAAI,CAAC,CAAC;MAC1BQ,UAAU,EAAE,IAAI1C,IAAI,CAAC;IACvB,CAAC;IAEDL,WAAW,CAACgD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,MAAM,CAAC,CAAC;IACtC/B,aAAa,CAAC,EAAE,CAAC;IACjBa,kBAAkB,CAAC,KAAK,CAAC;IACzBJ,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EAED,MAAM+B,YAAY,GAAIZ,CAAC,IAAK;IAC1B5B,aAAa,CAAC4B,CAAC,CAACa,MAAM,CAACC,KAAK,CAAC;IAE7B,IAAI,CAAClC,MAAM,EAAE;MACXC,SAAS,CAAC,IAAI,CAAC;IACjB;IAEAiB,YAAY,CAACX,gBAAgB,CAAC4B,OAAO,CAAC;IACtC5B,gBAAgB,CAAC4B,OAAO,GAAGlB,UAAU,CAAC,MAAM;MAC1ChB,SAAS,CAAC,KAAK,CAAC;IAClB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMmC,QAAQ,GAAIvB,KAAK,IAAK;IAC1BrB,aAAa,CAACuC,IAAI,IAAIA,IAAI,GAAGlB,KAAK,CAAC;IACnCR,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMU,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAsB,qBAAA;IAC3B,CAAAA,qBAAA,GAAA/B,cAAc,CAAC6B,OAAO,cAAAE,qBAAA,uBAAtBA,qBAAA,CAAwBC,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED,MAAMC,cAAc,GAAIpB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACqB,GAAG,KAAK,OAAO,IAAI,CAACrB,CAAC,CAACsB,QAAQ,EAAE;MACpCtB,CAAC,CAACC,cAAc,CAAC,CAAC;MAClBF,WAAW,CAACC,CAAC,CAAC;IAChB;EACF,CAAC;EAED,MAAMuB,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAO,IAAIxD,IAAI,CAACwD,IAAI,CAAC,CAACC,kBAAkB,CAAC,EAAE,EAAE;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAU,CAAC,CAAC;EACtF,CAAC;EAED,IAAIjD,OAAO,EAAE;IACX,oBACEtB,OAAA;MAAKwE,SAAS,EAAC,mGAAmG;MAAAC,QAAA,eAChHzE,OAAA;QAAKwE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BzE,OAAA;UAAKwE,SAAS,EAAC;QAA6F;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnH7E,OAAA;UAAGwE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE7E,OAAA;IAAKwE,SAAS,EAAC,mGAAmG;IAAAC,QAAA,gBAEhHzE,OAAA;MAAKwE,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClE,CAAC,GAAGK,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBjF,OAAA;QAEEwE,SAAS,EAAC,mCAAmC;QAC7CU,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C,CAAE;QAAAZ,QAAA,EAEDQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,GAAGA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG;MAAI,GAT9DA,CAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUH,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7E,OAAA;MAAKwE,SAAS,EAAC,4BAA4B;MAAAC,QAAA,eACzCzE,OAAA;QAAKwE,SAAS,EAAC,mHAAmH;QAAAC,QAAA,gBAEhIzE,OAAA;UAAKwE,SAAS,EAAC,mIAAmI;UAAAC,QAAA,gBAChJzE,OAAA;YAAKwE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzE,OAAA;cAAKwE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CzE,OAAA;gBAAKwE,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBzE,OAAA;kBAAKwE,SAAS,EAAE,2CAA2CvC,aAAa,CAAChB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACmB,QAAQ,uHAAwH;kBAAAmC,QAAA,EACtOxC,aAAa,CAAChB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACkB;gBAAK;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACN7E,OAAA;kBAAKwE,SAAS,EAAC;gBAA6G;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChI,CAAC,eACN7E,OAAA;gBAAAyE,QAAA,gBACEzE,OAAA;kBAAIwE,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClD,CAAAxD,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,KAAI;gBAAc;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACL7E,OAAA;kBAAGwE,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACnD/C,eAAe,gBACd1B,OAAA;oBAAMwE,SAAS,EAAC,8CAA8C;oBAAAC,QAAA,gBAC5DzE,OAAA;sBAAKwE,SAAS,EAAC,qBAAqB;sBAAAC,QAAA,gBAClCzE,OAAA;wBAAKwE,SAAS,EAAC,kDAAkD;wBAACU,KAAK,EAAE;0BAACK,cAAc,EAAE;wBAAI;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACvG7E,OAAA;wBAAKwE,SAAS,EAAC,kDAAkD;wBAACU,KAAK,EAAE;0BAACK,cAAc,EAAE;wBAAM;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACzG7E,OAAA;wBAAKwE,SAAS,EAAC,kDAAkD;wBAACU,KAAK,EAAE;0BAACK,cAAc,EAAE;wBAAM;sBAAE;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtG,CAAC,aAER;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,gBAEP7E,OAAA;oBAAMwE,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAChDzE,OAAA;sBAAKwE,SAAS,EAAC;oBAAsD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,cAE9E;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7E,OAAA;cAAKwE,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CzE,OAAA;gBAAKwE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChD7E,OAAA;gBAAKwE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzE,OAAA,CAACJ,IAAI;kBAAC4E,SAAS,EAAC;gBAAuC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1D7E,OAAA,CAACH,QAAQ;kBAAC2E,SAAS,EAAC;gBAAuC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7E,OAAA;YAAKwE,SAAS,EAAC;UAAoI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvJ,CAAC,eAGN7E,OAAA;UAAKwE,SAAS,EAAC,qDAAqD;UAAAC,QAAA,GACjEnE,QAAQ,CAAC0C,MAAM,KAAK,CAAC,gBACpBhD,OAAA;YAAKwE,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CzE,OAAA,CAACF,aAAa;cAAC0E,SAAS,EAAC;YAAqD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjF7E,OAAA;cAAGwE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC7D7E,OAAA;cAAGwE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,GAENvE,QAAQ,CAACyE,GAAG,CAAC,CAAC1B,OAAO,EAAEoC,KAAK,kBAC1BzF,OAAA;YAEEwE,SAAS,EAAE,QAAQnB,OAAO,CAACJ,SAAS,KAAK9C,IAAI,CAACK,EAAE,GAAG,aAAa,GAAG,eAAe,yDAA0D;YAC5I0E,KAAK,EAAE;cAAEK,cAAc,EAAE,GAAGE,KAAK,GAAG,GAAG;YAAI,CAAE;YAAAhB,QAAA,eAE7CzE,OAAA;cAAKwE,SAAS,EAAE,qCAAqCnB,OAAO,CAACJ,SAAS,KAAK9C,IAAI,CAACK,EAAE,GAAG,kCAAkC,GAAG,EAAE,EAAG;cAAAiE,QAAA,gBAC7HzE,OAAA;gBAAKwE,SAAS,EAAE,2CAA2CvC,aAAa,CAACoB,OAAO,CAACJ,SAAS,EAAEI,OAAO,CAACH,WAAW,CAAC,CAACZ,QAAQ,sHAAuH;gBAAAmC,QAAA,EAC7OxC,aAAa,CAACoB,OAAO,CAACJ,SAAS,EAAEI,OAAO,CAACH,WAAW,CAAC,CAACb;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eAEN7E,OAAA;gBAAKwE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BzE,OAAA;kBACEwE,SAAS,EAAE,yFACTnB,OAAO,CAACJ,SAAS,KAAK9C,IAAI,CAACK,EAAE,GACzB,uGAAuG,GACvG,4GAA4G,EAC/G;kBAAAiE,QAAA,gBAEHzE,OAAA;oBAAGwE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAEpB,OAAO,CAACA;kBAAO;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxE7E,OAAA;oBAAGwE,SAAS,EAAE,gBAAgBnB,OAAO,CAACJ,SAAS,KAAK9C,IAAI,CAACK,EAAE,GAAG,eAAe,GAAG,eAAe,EAAG;oBAAAiE,QAAA,EAC/FN,UAAU,CAACd,OAAO,CAACC,UAAU;kBAAC;oBAAAoB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC,eAGJ7E,OAAA;oBAAKwE,SAAS,EAAC;kBAAsM;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzN,CAAC,eAGN7E,OAAA;kBAAKwE,SAAS,EAAE,YAAYnB,OAAO,CAACJ,SAAS,KAAK9C,IAAI,CAACK,EAAE,GAAG,mBAAmB,GAAG,kBAAkB,EAAG;kBAAAiE,QAAA,eACrGzE,OAAA;oBAAKwE,SAAS,EAAE,+BACdnB,OAAO,CAACJ,SAAS,KAAK9C,IAAI,CAACK,EAAE,GACzB,+CAA+C,GAC/C,2EAA2E;kBAC9E;oBAAAkE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAnCDxB,OAAO,CAAC7C,EAAE,IAAIiF,KAAK;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoCrB,CACN,CACF,EAGAnD,eAAe,iBACd1B,OAAA;YAAKwE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDzE,OAAA;cAAKwE,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCzE,OAAA;gBAAKwE,SAAS,EAAE,2CAA2CvC,aAAa,CAAChB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACmB,QAAQ,8EAA+E;gBAAAmC,QAAA,EAC7LxC,aAAa,CAAChB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAET,EAAE,EAAES,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEE,IAAI,CAAC,CAACkB;cAAK;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN7E,OAAA;gBAAKwE,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAC3HzE,OAAA;kBAAKwE,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BzE,OAAA;oBAAKwE,SAAS,EAAC,iDAAiD;oBAACU,KAAK,EAAE;sBAACK,cAAc,EAAE;oBAAI;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtG7E,OAAA;oBAAKwE,SAAS,EAAC,iDAAiD;oBAACU,KAAK,EAAE;sBAACK,cAAc,EAAE;oBAAM;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxG7E,OAAA;oBAAKwE,SAAS,EAAC,iDAAiD;oBAACU,KAAK,EAAE;sBAACK,cAAc,EAAE;oBAAM;kBAAE;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED7E,OAAA;YAAK0F,GAAG,EAAE5D;UAAe;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAGN7E,OAAA;UAAKwE,SAAS,EAAC,yGAAyG;UAAAC,QAAA,GAErH7C,eAAe,iBACd5B,OAAA;YAAKwE,SAAS,EAAC,yIAAyI;YAAAC,QAAA,eACtJzE,OAAA;cAAKwE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,EACpCzC,MAAM,CAAC+C,GAAG,CAAC,CAAC1C,KAAK,EAAEoD,KAAK,kBACvBzF,OAAA;gBAEE2F,OAAO,EAAEA,CAAA,KAAM/B,QAAQ,CAACvB,KAAK,CAAE;gBAC/BmC,SAAS,EAAC,oHAAoH;gBAC9HU,KAAK,EAAE;kBAAEK,cAAc,EAAE,GAAGE,KAAK,GAAG,IAAI;gBAAI,CAAE;gBAAAhB,QAAA,EAE7CpC;cAAK,GALDoD,KAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAED7E,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBzE,OAAA;cAAKwE,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCzE,OAAA;gBACE2F,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC,CAACD,eAAe,CAAE;gBACpD4C,SAAS,EAAC,mNAAmN;gBAAAC,QAAA,eAE7NzE,OAAA,CAACR,KAAK;kBAACgF,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC,eAET7E,OAAA;gBAAKwE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BzE,OAAA;kBACE0D,KAAK,EAAE3C,UAAW;kBAClB6E,QAAQ,EAAEpC,YAAa;kBACvBqC,UAAU,EAAE7B,cAAe;kBAC3B8B,WAAW,EAAC,yBAAyB;kBACrCC,IAAI,EAAE,CAAE;kBACRvB,SAAS,EAAC;gBAAgQ;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3Q,CAAC,eACF7E,OAAA;kBAAKwE,SAAS,EAAC;gBAA6K;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChM,CAAC,eAEN7E,OAAA;gBACE2F,OAAO,EAAEhD,WAAY;gBACrBqD,QAAQ,EAAE,CAACjF,UAAU,CAAC+B,IAAI,CAAC,CAAE;gBAC7B0B,SAAS,EAAC,6RAA6R;gBAAAC,QAAA,gBAEvSzE,OAAA,CAACV,IAAI;kBAACkF,SAAS,EAAC;gBAA+E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClG7E,OAAA;kBAAKwE,SAAS,EAAC;gBAAuK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7E,OAAA;cAAKwE,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBACtEzE,OAAA;gBAAMwE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC3CzE,OAAA,CAACT,KAAK;kBAACiF,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3C7E,OAAA;kBAAAyE,QAAA,EAAM;gBAA8C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACP7E,OAAA;gBAAMwE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC3CzE,OAAA,CAACH,QAAQ;kBAAC2E,SAAS,EAAC;gBAAyB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChD7E,OAAA;kBAAAyE,QAAA,GAAO1D,UAAU,CAACiC,MAAM,EAAC,MAAI;gBAAA;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7E,OAAA;MAAOiG,GAAG;MAAAxB,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC3E,EAAA,CAlZID,IAAI;EAAA,QACmBb,OAAO,EACbC,QAAQ;AAAA;AAAA6G,EAAA,GAFzBjG,IAAI;AAoZV,eAAeA,IAAI;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
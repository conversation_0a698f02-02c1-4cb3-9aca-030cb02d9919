import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { Settings, Menu, X } from 'lucide-react';
import Home from './Home';
import Chat from './Chat';
import Photos from './Photos';
import Notes from './Notes';
import SettingsPanel from './SettingsPanel';
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('home');
  const [showSettings, setShowSettings] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const { user } = useAuth();
  const { darkMode } = useTheme();

  // Keyboard shortcuts
  useKeyboardShortcuts({
    '0': () => setActiveTab('home'),
    '1': () => setActiveTab('chat'),
    '2': () => setActiveTab('photos'),
    '3': () => setActiveTab('notes'),
    'escape': () => {
      setShowSettings(false);
      setShowMobileMenu(false);
    },
    's': () => setShowSettings(true)
  });

  const tabs = [
    { 
      id: 'home', 
      name: 'Home', 
      icon: '🏠', 
      shortcut: '0',
      gradient: 'from-indigo-500 to-purple-500',
      description: 'Your sanctuary'
    },
    { 
      id: 'chat', 
      name: 'Our Chats', 
      icon: '💬', 
      shortcut: '1',
      gradient: 'from-pink-500 to-rose-500',
      description: 'Sweet conversations'
    },
    { 
      id: 'photos', 
      name: 'Memories', 
      icon: '📸', 
      shortcut: '2',
      gradient: 'from-purple-500 to-indigo-500',
      description: 'Beautiful moments'
    },
    { 
      id: 'notes', 
      name: 'Love Notes', 
      icon: '💕', 
      shortcut: '3',
      gradient: 'from-red-500 to-pink-500',
      description: 'Heartfelt messages'
    }
  ];

  const getTabTitle = () => {
    switch(activeTab) {
      case 'home': return 'Welcome Home';
      case 'chat': return 'Our Sweet Conversations';
      case 'photos': return 'Our Beautiful Memories';
      case 'notes': return 'Love Notes & Thoughts';
      default: return 'Our Love Story';
    }
  };

  const getTabIcon = () => {
    switch(activeTab) {
      case 'home': return '🏠';
      case 'chat': return '💬';
      case 'photos': return '📸';
      case 'notes': return '💕';
      default: return '💖';
    }
  };

  const getTabGradient = () => {
    const tab = tabs.find(t => t.id === activeTab);
    return tab?.gradient || 'from-pink-500 to-rose-500';
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      darkMode 
        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900' 
        : 'bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50'
    } relative overflow-hidden`}>
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute top-10 left-10 w-32 h-32 ${
          darkMode ? 'bg-purple-500/20' : 'bg-pink-200'
        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse`}></div>
        <div className={`absolute top-32 right-20 w-40 h-40 ${
          darkMode ? 'bg-blue-500/20' : 'bg-purple-200'
        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000`}></div>
        <div className={`absolute bottom-32 left-1/4 w-28 h-28 ${
          darkMode ? 'bg-pink-500/20' : 'bg-rose-200'
        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000`}></div>
      </div>

      {/* Header */}
      <header className={`relative z-10 backdrop-blur-sm ${
        darkMode 
          ? 'bg-gray-900/80 border-gray-700/50' 
          : 'bg-white/80 border-pink-200/50'
      } shadow-lg border-b transition-colors duration-300`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                <span className="text-2xl">💖</span>
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                  Our Love Story
                </h1>
                <p className={`text-sm font-medium ${
                  darkMode ? 'text-gray-400' : 'text-gray-500'
                }`}>Together forever ∞</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="text-right hidden sm:block">
                <p className={`font-medium ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>Welcome back,</p>
                <p className="text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent">
                  {user?.name} 💕
                </p>
              </div>
              
              {/* Settings Button */}
              <button
                onClick={() => setShowSettings(true)}
                className={`p-3 ${
                  darkMode 
                    ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' 
                    : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'
                } rounded-full border transition-all duration-200 hover:shadow-md focus:outline-none 
                         focus:ring-2 focus:ring-pink-300 group`}
                title="Settings (Press S)"
              >
                <Settings size={20} className="group-hover:rotate-90 transition-transform duration-300" />
              </button>
              
              {/* Mobile Menu Button */}
              <button
                onClick={() => setShowMobileMenu(!showMobileMenu)}
                className={`sm:hidden p-3 ${
                  darkMode 
                    ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' 
                    : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'
                } rounded-full border transition-all duration-200 hover:shadow-md`}
              >
                {showMobileMenu ? <X size={20} /> : <Menu size={20} />}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className={`relative z-10 ${
        darkMode 
          ? 'bg-gray-800/60 border-gray-700/50' 
          : 'bg-white/60 border-pink-100/50'
      } backdrop-blur-sm border-b transition-colors duration-300`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`}>
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => {
                  setActiveTab(tab.id);
                  setShowMobileMenu(false);
                }}
                className={`flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${
                  activeTab === tab.id
                    ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105`
                    : `${
                        darkMode 
                          ? 'text-gray-300 hover:text-white hover:bg-gray-700/70' 
                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/70'
                      } hover:shadow-md`
                }`}
              >
                <span className="text-2xl group-hover:scale-110 transition-transform duration-200">
                  {tab.icon}
                </span>
                <div className="text-left">
                  <div className="font-bold">{tab.name}</div>
                  <div className={`text-xs ${
                    activeTab === tab.id 
                      ? 'text-white/80' 
                      : darkMode ? 'text-gray-400' : 'text-gray-500'
                  }`}>
                    {tab.description} • Press {tab.shortcut}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8">
        <div className="px-4 sm:px-0">
          <div className={`${
            darkMode 
              ? 'bg-gray-800/70 border-gray-700/50' 
              : 'bg-white/70 border-white/50'
          } backdrop-blur-sm rounded-3xl shadow-2xl border p-8 
                         transition-all duration-500 hover:shadow-3xl min-h-[600px]`}>
            
            {/* Tab Header - Only show for non-home tabs */}
            {activeTab !== 'home' && (
              <div className="flex items-center space-x-3 mb-6">
                <div className={`w-10 h-10 bg-gradient-to-r ${getTabGradient()} rounded-full flex items-center justify-center`}>
                  <span className="text-white text-lg">{getTabIcon()}</span>
                </div>
                <h2 className={`text-2xl font-bold ${
                  darkMode ? 'text-white' : 'text-gray-800'
                }`}>{getTabTitle()}</h2>
              </div>
            )}

            <div className="transition-all duration-300">
              {activeTab === 'home' && (
                <div className="animate-fade-in">
                  <Home />
                </div>
              )}
              {activeTab === 'chat' && (
                <div className="animate-fade-in">
                  <Chat />
                </div>
              )}
              {activeTab === 'photos' && (
                <div className="animate-fade-in">
                  <Photos />
                </div>
              )}
              {activeTab === 'notes' && (
                <div className="animate-fade-in">
                  <Notes />
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Settings Panel */}
      <SettingsPanel 
        isOpen={showSettings} 
        onClose={() => setShowSettings(false)} 
      />
    </div>
  );
};

export default Dashboard;




import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';

const LoadingTransition = ({ onComplete }) => {
  const [progress, setProgress] = useState(0);
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const slides = [
    { emoji: '💕', text: 'Loading our love story...' },
    { emoji: '🌹', text: 'Preparing beautiful moments...' },
    { emoji: '✨', text: 'Creating magical memories...' },
    { emoji: '💖', text: 'Almost ready for you...' }
  ];

  useEffect(() => {
    const slideInterval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % slides.length);
    }, 1000);

    const progressInterval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          setTimeout(onComplete, 200);
          return 100;
        }
        return prev + 5;
      });
    }, 50);

    return () => {
      clearInterval(slideInterval);
      clearInterval(progressInterval);
    };
  }, [onComplete, slides.length]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-pink-100 via-purple-50 to-indigo-100 overflow-hidden">
      {/* Floating hearts animation */}
      <div className="absolute inset-0">
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${4 + Math.random() * 2}s`
            }}
          >
            <Heart className="text-pink-400" size={20 + Math.random() * 20} />
          </div>
        ))}
      </div>

      <div className="relative z-10 text-center max-w-md mx-auto px-8">
        {/* Main loading content */}
        <div className="mb-8">
          <div className="text-8xl mb-6 animate-bounce">
            {slides[currentSlide].emoji}
          </div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4 animate-pulse">
            {slides[currentSlide].text}
          </h2>
        </div>

        {/* Progress bar */}
        <div className="w-full bg-white/50 rounded-full h-3 mb-4 overflow-hidden shadow-inner">
          <div 
            className="h-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 rounded-full transition-all duration-300 ease-out relative overflow-hidden"
            style={{ width: `${progress}%` }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer"></div>
          </div>
        </div>

        <p className="text-sm text-gray-600 animate-pulse">
          {progress}% Complete
        </p>
      </div>
    </div>
  );
};

export default LoadingTransition;



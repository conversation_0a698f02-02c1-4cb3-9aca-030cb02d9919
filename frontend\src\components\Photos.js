import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { Plus, Search, Grid, List } from 'lucide-react';

// Mock API for photos
const photosAPI = {
  getPhotos: () => Promise.resolve({ data: [] }),
  uploadPhoto: (formData) => Promise.resolve({ data: { id: Date.now(), photo_url: '/uploads/sample.jpg' } }),
  deletePhoto: (id) => Promise.resolve(true)
};

const Photos = () => {
  const { user } = useAuth();
  const { darkMode } = useTheme();
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [caption, setCaption] = useState('');
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onload = (e) => setPreviewUrl(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  useEffect(() => {
    loadPhotos();
  }, []);

  const loadPhotos = async () => {
    setLoading(false);
  };

  if (loading) {
    return (
      <div className={`min-h-screen ${
        darkMode 
          ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900' 
          : 'bg-gradient-to-br from-rose-50 via-pink-50 to-red-50'
      } flex items-center justify-center`}>
        <div className="text-center py-16 animate-fadeIn">
          <div className="relative mb-8">
            <div className="w-20 h-20 mx-auto">
              <div className={`absolute inset-0 border-4 ${
                darkMode ? 'border-purple-500' : 'border-rose-200'
              } rounded-full animate-spin`}></div>
              <div className={`absolute inset-2 border-4 ${
                darkMode ? 'border-pink-400' : 'border-rose-400'
              } rounded-full animate-spin`} style={{animationDirection: 'reverse', animationDuration: '1s'}}></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl animate-pulse">💕</span>
              </div>
            </div>
          </div>
          <p className={`text-lg font-medium animate-pulse ${
            darkMode ? 'text-purple-300' : 'text-rose-600'
          }`}>Loading our beautiful journey together...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-pink-50 to-red-50 relative overflow-hidden">
      {/* Animated background hearts */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float-hearts opacity-20"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              fontSize: `${12 + Math.random() * 20}px`,
              animationDelay: `${i * 0.8}s`,
              animationDuration: `${8 + Math.random() * 4}s`
            }}
          >
            {['💕', '💖', '💗', '💝', '💘', '🌹', '✨', '🦋'][Math.floor(Math.random() * 8)]}
          </div>
        ))}
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 space-y-12">
        
        {/* Romantic Header */}
        <div className="text-center animate-slideInDown">
          <h1 className="text-5xl font-bold bg-gradient-to-r from-rose-600 via-pink-500 to-red-500 bg-clip-text text-transparent mb-4">
            Our Love Story 💕
          </h1>
          <p className="text-rose-500 text-lg font-medium">
            Every picture tells a story, every moment is a treasure ✨
          </p>
          <div className="flex justify-center space-x-4 mt-4 text-3xl">
            <span className="animate-bounce" style={{animationDelay: '0s'}}>💖</span>
            <span className="animate-bounce" style={{animationDelay: '0.2s'}}>🌹</span>
            <span className="animate-bounce" style={{animationDelay: '0.4s'}}>💕</span>
          </div>
        </div>

        {/* Upload Section */}
        <div className="relative max-w-2xl mx-auto">
          <div className="glass-romantic rounded-3xl shadow-2xl border border-rose-200 p-8 animate-slideInLeft relative overflow-hidden backdrop-blur-sm">
            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-bl from-rose-300 to-pink-300 rounded-full opacity-20 -mr-20 -mt-20 animate-float"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-red-300 to-pink-300 rounded-full opacity-15 -ml-16 -mb-16 animate-float" style={{animationDelay: '2s'}}></div>
            
            <div className="relative z-10">
              <div className="text-center mb-8">
                <div className="inline-flex items-center space-x-4 bg-white bg-opacity-60 rounded-full px-6 py-3 shadow-lg">
                  <span className="text-4xl animate-heartbeat">📸</span>
                  <h3 className="text-2xl font-bold text-rose-800">
                    Capture Our Moment
                  </h3>
                  <span className="text-3xl animate-sparkle">✨</span>
                </div>
                <p className="text-rose-600 mt-4 text-sm">
                  Share a piece of your heart with every photo 💝
                </p>
              </div>
              
              <form onSubmit={uploadPhoto} className="space-y-6">
                {/* File Input */}
                <div className="relative">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="hidden"
                    id="photo-upload"
                  />
                  <label
                    htmlFor="photo-upload"
                    className="block w-full p-8 border-3 border-dashed border-rose-300 rounded-3xl text-center cursor-pointer transition-all duration-500 hover:border-rose-500 hover:bg-gradient-to-br hover:from-rose-50 hover:to-pink-50 hover:shadow-lg transform hover:scale-105 bg-white bg-opacity-50"
                  >
                    {previewUrl ? (
                      <div className="space-y-4">
                        <div className="relative inline-block">
                          <img
                            src={previewUrl}
                            alt="Preview"
                            className="max-h-48 mx-auto rounded-2xl shadow-2xl animate-bounceIn border-4 border-white"
                          />
                          <div className="absolute -top-2 -right-2 text-2xl animate-heartbeat">💖</div>
                        </div>
                        <p className="text-rose-600 font-medium">Perfect! Click to choose a different moment</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div className="text-8xl animate-float mb-4">💝</div>
                        <div>
                          <p className="text-xl font-bold text-rose-700 mb-2">Share Your Beautiful Memory</p>
                          <p className="text-rose-500">Drop your photo here or click to browse</p>
                          <p className="text-sm text-rose-400 mt-2">Every moment with you is picture-perfect 📷💕</p>
                        </div>
                      </div>
                    )}
                  </label>
                </div>

                {/* Caption Input */}
                <div className="relative">
                  <div className="absolute -top-3 left-6 bg-gradient-to-r from-rose-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                    💕 Share your feelings
                  </div>
                  <textarea
                    value={caption}
                    onChange={(e) => setCaption(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="What makes this moment special, my love? Share your heart here... 💖"
                    rows={4}
                    className="w-full px-8 py-6 border-2 border-rose-300 rounded-2xl focus:outline-none focus:ring-4 focus:ring-rose-200 focus:border-rose-500 resize-none transition-all duration-300 bg-white bg-opacity-80 shadow-inner text-gray-800 placeholder-rose-400 text-lg backdrop-blur-sm"
                    style={{
                      background: 'linear-gradient(145deg, rgba(255,255,255,0.9), rgba(255,242,248,0.9))'
                    }}
                  />
                </div>

                {/* Upload Button */}
                <button
                  type="submit"
                  disabled={!selectedFile || uploading}
                  className="w-full py-5 bg-gradient-to-r from-rose-500 via-pink-500 to-red-500 hover:from-rose-600 hover:via-pink-600 hover:to-red-600 text-white rounded-2xl font-bold text-xl transition-all duration-500 disabled:opacity-50 shadow-2xl transform hover:scale-105 hover:shadow-3xl disabled:transform-none relative overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white to-transparent opacity-20 animate-shimmer"></div>
                  {uploading ? (
                    <div className="flex items-center justify-center space-x-3 relative z-10">
                      <div className="animate-spin">
                        <span className="text-2xl">💕</span>
                      </div>
                      <span>Preserving our beautiful moment...</span>
                      <span className="text-2xl animate-pulse">✨</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center space-x-3 relative z-10">
                      <span className="text-2xl animate-heartbeat">💖</span>
                      <span>Share This Memory</span>
                      <span className="text-2xl animate-bounce">🌹</span>
                    </div>
                  )}
                </button>
                
                <div className="text-center">
                  <p className="text-sm text-rose-500 bg-white bg-opacity-60 rounded-full py-3 px-6 inline-flex items-center space-x-2">
                    <span>💡</span>
                    <span>Press Enter to share • Shift+Enter for new line</span>
                    <span>💕</span>
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>

        {/* Photos Grid */}
        <div className="glass-romantic rounded-3xl shadow-2xl border border-rose-200 p-8 animate-slideInRight relative overflow-hidden backdrop-blur-sm">
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-40 h-40 bg-gradient-to-br from-pink-300 to-red-300 rounded-full opacity-15 -ml-20 -mt-20 animate-float" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-0 right-0 w-36 h-36 bg-gradient-to-tl from-rose-300 to-pink-300 rounded-full opacity-20 -mr-18 -mb-18 animate-float" style={{animationDelay: '3s'}}></div>
          
          <div className="relative z-10">
            <div className="text-center mb-10">
              <div className="inline-flex items-center space-x-4 bg-white bg-opacity-70 rounded-full px-8 py-4 shadow-xl">
                <span className="text-4xl animate-heartbeat">💕</span>
                <h3 className="text-3xl font-bold text-rose-800">
                  Our Memory Lane
                </h3>
                <span className="text-4xl animate-sparkle">🌟</span>
              </div>
              <p className="text-rose-600 mt-4 font-medium">
                A collection of moments that make our hearts flutter 🦋💖
              </p>
            </div>
            
            {photos.length === 0 ? (
              <div className="text-center text-rose-400 py-20 animate-fadeIn">
                <div className="text-9xl mb-8 animate-float">💝</div>
                <h4 className="text-2xl font-bold text-rose-600 mb-4">No memories yet, my love</h4>
                <p className="text-lg text-rose-500 mb-2">Let's start creating our beautiful story together!</p>
                <p className="text-sm text-rose-400">Upload your first precious moment and watch our gallery bloom like a garden of love 🌹</p>
                <div className="flex justify-center space-x-3 mt-6 text-2xl">
                  <span className="animate-bounce" style={{animationDelay: '0s'}}>💕</span>
                  <span className="animate-bounce" style={{animationDelay: '0.3s'}}>🌺</span>
                  <span className="animate-bounce" style={{animationDelay: '0.6s'}}>💖</span>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                {photos.map((photo, index) => (
                  <div
                    key={photo.id}
                    className="photo-item-romantic glass-card rounded-3xl overflow-hidden shadow-xl border border-white border-opacity-30 animate-fadeIn transform hover:scale-105 transition-all duration-500 hover:shadow-2xl relative"
                    style={{animationDelay: `${index * 0.15}s`}}
                    onClick={() => setSelectedPhoto(photo)}
                  >
                    <div className="relative group cursor-pointer overflow-hidden">
                      <img
                        src={`http://localhost:5000${photo.photo_url}`}
                        alt={photo.caption || 'Our precious memory'}
                        className="w-full h-56 object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                      />
                      
                      {/* Romantic overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-0 group-hover:opacity-80 transition-all duration-500">
                        <div className="absolute bottom-4 left-4 right-4 text-white transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                          <p className="text-sm font-medium truncate mb-1">{photo.caption || 'A beautiful moment'}</p>
                          <div className="flex items-center justify-between text-xs opacity-90">
                            <span>{new Date(photo.uploaded_at).toLocaleDateString()}</span>
                            <span className="flex items-center space-x-1">
                              <span>💕</span>
                              <span>{photo.uploader_name}</span>
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Love button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleLove(photo.id);
                        }}
                        className="absolute top-3 right-3 w-10 h-10 bg-white bg-opacity-90 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white hover:scale-110 flex items-center justify-center shadow-lg"
                      >
                        <span className={`text-xl transition-all duration-300 ${isLoved[photo.id] ? 'animate-heartbeat text-red-500' : 'text-gray-400 hover:text-red-500'}`}>
                          {isLoved[photo.id] ? '💖' : '🤍'}
                        </span>
                      </button>

                      {/* Delete button for own photos */}
                      {photo.user_id === user.id && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deletePhoto(photo.id);
                          }}
                          className="absolute top-3 left-3 w-10 h-10 bg-red-500 bg-opacity-90 text-white rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-red-600 hover:scale-110 flex items-center justify-center shadow-lg"
                        >
                          <span className="text-lg">×</span>
                        </button>
                      )}

                      {/* Corner decoration */}
                      <div className="absolute top-2 left-2 text-xl opacity-0 group-hover:opacity-100 transition-all duration-500 animate-sparkle">
                        ✨
                      </div>
                    </div>
                    
                    <div className="p-5 bg-gradient-to-br from-white to-rose-50">
                      <div className="flex items-center justify-between text-xs text-rose-400 mb-2">
                        <span className="flex items-center space-x-1">
                          <span>💕</span>
                          <span>By {photo.uploader_name}</span>
                        </span>
                        <span>{new Date(photo.uploaded_at).toLocaleDateString()}</span>
                      </div>
                      {photo.caption && (
                        <p className="text-sm text-gray-700 line-clamp-2 leading-relaxed">{photo.caption}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Photo Modal */}
        {selectedPhoto && (
          <div 
            className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4 animate-fadeIn backdrop-blur-sm" 
            onClick={() => setSelectedPhoto(null)}
          >
            <div 
              className="max-w-5xl max-h-full bg-white rounded-3xl overflow-hidden shadow-2xl animate-bounceIn border-4 border-rose-200 relative" 
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <img
                  src={`http://localhost:5000${selectedPhoto.photo_url}`}
                  alt={selectedPhoto.caption || 'Our beautiful memory'}
                  className="w-full max-h-[70vh] object-contain bg-gradient-to-br from-rose-50 to-pink-50"
                />
                <button
                  onClick={() => setSelectedPhoto(null)}
                  className="absolute top-6 right-6 w-12 h-12 bg-rose-500 bg-opacity-90 hover:bg-rose-600 text-white rounded-full hover:scale-110 transition-all duration-300 flex items-center justify-center shadow-xl"
                >
                  <span className="text-xl">×</span>
                </button>
                
                {/* Floating hearts around the image */}
                <div className="absolute top-4 left-4 text-2xl animate-float opacity-70">💕</div>
                <div className="absolute bottom-4 right-4 text-xl animate-heartbeat opacity-70">💖</div>
                <div className="absolute top-1/2 left-4 text-lg animate-sparkle opacity-60">✨</div>
              </div>
              
              <div className="p-8 bg-gradient-to-br from-rose-50 to-pink-50">
                <div className="text-center">
                  <h4 className="text-2xl font-bold text-rose-800 mb-3 flex items-center justify-center space-x-3">
                    <span className="animate-heartbeat">💝</span>
                    <span>{selectedPhoto.caption || 'A Beautiful Memory Together'}</span>
                    <span className="animate-sparkle">✨</span>
                  </h4>
                  <p className="text-rose-600 text-lg">
                    Shared with love by <strong>{selectedPhoto.uploader_name}</strong> on {new Date(selectedPhoto.uploaded_at).toLocaleDateString()}
                  </p>
                  <div className="flex justify-center space-x-3 mt-4 text-2xl">
                    <span className="animate-bounce" style={{animationDelay: '0s'}}>🌹</span>
                    <span className="animate-bounce" style={{animationDelay: '0.2s'}}>💕</span>
                    <span className="animate-bounce" style={{animationDelay: '0.4s'}}>🦋</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Custom Styles */}
      <style jsx>{`
        @keyframes float-hearts {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-20px) rotate(5deg); }
          66% { transform: translateY(-10px) rotate(-3deg); }
        }
        
        @keyframes heartbeat {
          0%, 50%, 100% { transform: scale(1); }
          25%, 75% { transform: scale(1.1); }
        }
        
        @keyframes sparkle {
          0%, 100% { opacity: 0.6; transform: scale(1); }
          50% { opacity: 1; transform: scale(1.2); }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        .glass-romantic {
          background: rgba(255, 255, 255, 0.25);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(244, 63, 94, 0.1);
        }
        
        .glass-card {
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(5px);
        }
        
        .animate-float-hearts {
          animation: float-hearts infinite ease-in-out;
        }
        
        .animate-heartbeat {
          animation: heartbeat 1.5s ease-in-out infinite;
        }
        
        .animate-sparkle {
          animation: sparkle 2s ease-in-out infinite;
        }
        
        .animate-shimmer {
          animation: shimmer 2s linear infinite;
        }
        
        .photo-item-romantic:hover .glass-card {
          background: rgba(255, 255, 255, 0.95);
        }
      `}</style>
    </div>
  );
};

export default Photos;





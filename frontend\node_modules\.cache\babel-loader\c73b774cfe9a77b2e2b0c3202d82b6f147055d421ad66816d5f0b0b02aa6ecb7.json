{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\context\\\\ThemeContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ThemeContext = /*#__PURE__*/createContext();\nexport const useTheme = () => {\n  _s();\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n_s(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const ThemeProvider = ({\n  children\n}) => {\n  _s2();\n  const [darkMode, setDarkMode] = useState(() => {\n    const saved = localStorage.getItem('darkMode');\n    return saved ? JSON.parse(saved) : false;\n  });\n  useEffect(() => {\n    localStorage.setItem('darkMode', JSON.stringify(darkMode));\n\n    // Apply dark class to document root AND body for global coverage\n    if (darkMode) {\n      document.documentElement.classList.add('dark');\n      document.body.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      document.body.classList.remove('dark');\n    }\n  }, [darkMode]);\n  const toggleDarkMode = () => {\n    setDarkMode(prev => !prev);\n  };\n  const value = {\n    darkMode,\n    toggleDarkMode\n  };\n  return /*#__PURE__*/_jsxDEV(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s2(ThemeProvider, \"x7Z21TzAWY4Qpk7L1/opiGIkAe8=\");\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "jsxDEV", "_jsxDEV", "ThemeContext", "useTheme", "_s", "context", "Error", "ThemeProvider", "children", "_s2", "darkMode", "setDarkMode", "saved", "localStorage", "getItem", "JSON", "parse", "setItem", "stringify", "document", "documentElement", "classList", "add", "body", "remove", "toggleDarkMode", "prev", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/context/ThemeContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\n\nconst ThemeContext = createContext();\n\nexport const useTheme = () => {\n  const context = useContext(ThemeContext);\n  if (!context) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n};\n\nexport const ThemeProvider = ({ children }) => {\n  const [darkMode, setDarkMode] = useState(() => {\n    const saved = localStorage.getItem('darkMode');\n    return saved ? JSON.parse(saved) : false;\n  });\n\n  useEffect(() => {\n    localStorage.setItem('darkMode', JSON.stringify(darkMode));\n    \n    // Apply dark class to document root AND body for global coverage\n    if (darkMode) {\n      document.documentElement.classList.add('dark');\n      document.body.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n      document.body.classList.remove('dark');\n    }\n  }, [darkMode]);\n\n  const toggleDarkMode = () => {\n    setDarkMode(prev => !prev);\n  };\n\n  const value = {\n    darkMode,\n    toggleDarkMode\n  };\n\n  return (\n    <ThemeContext.Provider value={value}>\n      {children}\n    </ThemeContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9E,MAAMC,YAAY,gBAAGN,aAAa,CAAC,CAAC;AAEpC,OAAO,MAAMO,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAMC,OAAO,GAAGR,UAAU,CAACK,YAAY,CAAC;EACxC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,QAAQ;AAQrB,OAAO,MAAMI,aAAa,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC7C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,MAAM;IAC7C,MAAMc,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAC9C,OAAOF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACJ,KAAK,CAAC,GAAG,KAAK;EAC1C,CAAC,CAAC;EAEFb,SAAS,CAAC,MAAM;IACdc,YAAY,CAACI,OAAO,CAAC,UAAU,EAAEF,IAAI,CAACG,SAAS,CAACR,QAAQ,CAAC,CAAC;;IAE1D;IACA,IAAIA,QAAQ,EAAE;MACZS,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;MAC9CH,QAAQ,CAACI,IAAI,CAACF,SAAS,CAACC,GAAG,CAAC,MAAM,CAAC;IACrC,CAAC,MAAM;MACLH,QAAQ,CAACC,eAAe,CAACC,SAAS,CAACG,MAAM,CAAC,MAAM,CAAC;MACjDL,QAAQ,CAACI,IAAI,CAACF,SAAS,CAACG,MAAM,CAAC,MAAM,CAAC;IACxC;EACF,CAAC,EAAE,CAACd,QAAQ,CAAC,CAAC;EAEd,MAAMe,cAAc,GAAGA,CAAA,KAAM;IAC3Bd,WAAW,CAACe,IAAI,IAAI,CAACA,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMC,KAAK,GAAG;IACZjB,QAAQ;IACRe;EACF,CAAC;EAED,oBACExB,OAAA,CAACC,YAAY,CAAC0B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAnB,QAAA,EACjCA;EAAQ;IAAAqB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACvB,GAAA,CAjCWF,aAAa;AAAA0B,EAAA,GAAb1B,aAAa;AAAA,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Calendar, Heart, MessageSquare, Camera, PenTool } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isHisAccount, setIsHisAccount] = useState(false);\n  useEffect(() => {\n    const timer = setInterval(() => setCurrentTime(new Date()), 1000);\n    return () => clearInterval(timer);\n  }, []);\n  useEffect(() => {\n    var _user$name, _user$email;\n    setIsHisAccount((user === null || user === void 0 ? void 0 : (_user$name = user.name) === null || _user$name === void 0 ? void 0 : _user$name.toLowerCase().includes('alex')) || (user === null || user === void 0 ? void 0 : (_user$email = user.email) === null || _user$email === void 0 ? void 0 : _user$email.includes('alex')));\n  }, [user]);\n  const NewsCard = ({\n    article,\n    type\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-xl p-4 border hover:shadow-lg transition-all duration-300 group cursor-pointer`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-start space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-2xl\",\n        children: article.image\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `px-2 py-1 text-xs font-medium rounded-full ${type === 'sports' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300' : 'bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-300'}`,\n            children: article.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n            children: article.time\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold mb-2 group-hover:text-blue-600 transition-colors ${darkMode ? 'text-white' : 'text-gray-800'}`,\n          children: article.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n          children: article.summary\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n            children: article.source\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ExternalLink, {\n            size: 14,\n            className: `${darkMode ? 'text-gray-400' : 'text-gray-500'} group-hover:text-blue-600 transition-colors`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-2xl p-6 border`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse\",\n            children: isHisAccount ? '👨‍💼' : '👩‍💄'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n              children: [\"Good \", currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening', \", \", user === null || user === void 0 ? void 0 : user.name, \"!\", isHisAccount ? ' 💪' : ' ✨']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n              children: currentTime.toLocaleDateString('en-US', {\n                weekday: 'long',\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-right\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n            children: currentTime.toLocaleTimeString('en-US', {\n              hour: '2-digit',\n              minute: '2-digit'\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(MapPin, {\n              size: 14,\n              className: \"text-pink-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: darkMode ? 'text-gray-300' : 'text-gray-600',\n              children: \"Your Love Sanctuary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gradient-to-r from-purple-900/50 to-pink-900/50' : 'bg-gradient-to-r from-pink-50 to-purple-50'} rounded-xl p-4 text-center`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-2 mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-pink-500 animate-pulse\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n            children: \"Daily Love Quote\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-pink-500 animate-pulse\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: `text-lg font-semibold italic ${darkMode ? 'text-white' : 'text-gray-800'}`,\n          children: [\"\\\"\", quotes[currentQuote], \"\\\"\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-xl p-4 border text-center`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl mb-2\",\n          children: \"\\uD83D\\uDC95\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n          children: \"365+\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n          children: \"Days Together\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-xl p-4 border text-center`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl mb-2\",\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n          children: \"1,247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n          children: \"Sweet Messages\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-xl p-4 border text-center`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl mb-2\",\n          children: \"\\uD83D\\uDCF8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n          children: \"89\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n          children: \"Beautiful Memories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-2xl p-6 border`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl\",\n            children: isHisAccount ? '⚽' : '💄'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: `text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n              children: isHisAccount ? 'Sports Updates' : 'Fashion & Beauty News'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`,\n              children: isHisAccount ? 'Latest from the sports world' : 'Trending in fashion and beauty'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `p-2 rounded-lg ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'} transition-colors`,\n          children: /*#__PURE__*/_jsxDEV(RefreshCw, {\n            size: 18,\n            className: `${newsLoading ? 'animate-spin' : ''} ${darkMode ? 'text-gray-400' : 'text-gray-600'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), newsLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [1, 2, 3].map(i => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${darkMode ? 'bg-gray-700/50' : 'bg-gray-100'} rounded-xl p-4 animate-pulse`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-12 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'} rounded-lg`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `h-4 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'} rounded w-3/4`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `h-3 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'} rounded w-1/2`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 17\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: (isHisAccount ? sportsNews : fashionNews).map(article => /*#__PURE__*/_jsxDEV(NewsCard, {\n          article: article,\n          type: isHisAccount ? 'sports' : 'fashion'\n        }, article.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'} backdrop-blur-sm rounded-2xl p-6 border`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n          className: \"text-purple-500\",\n          size: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: `text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n          children: \"Today's Love Agenda\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-pink-500/10 to-purple-500/10\",\n          children: [/*#__PURE__*/_jsxDEV(Coffee, {\n            className: \"text-pink-500\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: darkMode ? 'text-gray-300' : 'text-gray-700',\n            children: \"Morning coffee together \\u2615\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-blue-500/10 to-indigo-500/10\",\n          children: [/*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-blue-500\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: darkMode ? 'text-gray-300' : 'text-gray-700',\n            children: \"Share a sweet message \\uD83D\\uDC8C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10\",\n          children: [/*#__PURE__*/_jsxDEV(Star, {\n            className: \"text-purple-500\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: darkMode ? 'text-gray-300' : 'text-gray-700',\n            children: \"Plan weekend date night \\uD83C\\uDF1F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"gGyJsqIh7/MMkC9oifENp7naYIY=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useTheme", "Calendar", "Heart", "MessageSquare", "Camera", "PenTool", "jsxDEV", "_jsxDEV", "Home", "_s", "user", "darkMode", "currentTime", "setCurrentTime", "Date", "isHisAccount", "setIsHisAccount", "timer", "setInterval", "clearInterval", "_user$name", "_user$email", "name", "toLowerCase", "includes", "email", "NewsCard", "article", "type", "className", "children", "image", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "category", "time", "title", "summary", "source", "ExternalLink", "size", "getHours", "toLocaleDateString", "weekday", "year", "month", "day", "toLocaleTimeString", "hour", "minute", "MapPin", "quotes", "currentQuote", "RefreshCw", "newsLoading", "map", "i", "sportsNews", "fashionNews", "id", "Coffee", "Star", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Calendar, Heart, MessageSquare, Camera, PenTool } from 'lucide-react';\n\nconst Home = () => {\n  const { user } = useAuth();\n  const { darkMode } = useTheme();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isHisAccount, setIsHisAccount] = useState(false);\n\n  useEffect(() => {\n    const timer = setInterval(() => setCurrentTime(new Date()), 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  useEffect(() => {\n    setIsHisAccount(user?.name?.toLowerCase().includes('alex') || user?.email?.includes('alex'));\n  }, [user]);\n\n  const NewsCard = ({ article, type }) => (\n    <div className={`${\n      darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n    } backdrop-blur-sm rounded-xl p-4 border hover:shadow-lg transition-all duration-300 group cursor-pointer`}>\n      <div className=\"flex items-start space-x-3\">\n        <div className=\"text-2xl\">{article.image}</div>\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <span className={`px-2 py-1 text-xs font-medium rounded-full ${\n              type === 'sports' \n                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300'\n                : 'bg-pink-100 text-pink-800 dark:bg-pink-900/50 dark:text-pink-300'\n            }`}>\n              {article.category}\n            </span>\n            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n              {article.time}\n            </span>\n          </div>\n          <h3 className={`font-semibold mb-2 group-hover:text-blue-600 transition-colors ${\n            darkMode ? 'text-white' : 'text-gray-800'\n          }`}>\n            {article.title}\n          </h3>\n          <p className={`text-sm mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n            {article.summary}\n          </p>\n          <div className=\"flex items-center justify-between\">\n            <span className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>\n              {article.source}\n            </span>\n            <ExternalLink size={14} className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} group-hover:text-blue-600 transition-colors`} />\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Header */}\n      <div className={`${\n        darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n      } backdrop-blur-sm rounded-2xl p-6 border`}>\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center text-2xl animate-pulse\">\n              {isHisAccount ? '👨‍💼' : '👩‍💄'}\n            </div>\n            <div>\n              <h1 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n                Good {currentTime.getHours() < 12 ? 'Morning' : currentTime.getHours() < 18 ? 'Afternoon' : 'Evening'}, {user?.name}! \n                {isHisAccount ? ' 💪' : ' ✨'}\n              </h1>\n              <p className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                {currentTime.toLocaleDateString('en-US', { \n                  weekday: 'long', \n                  year: 'numeric', \n                  month: 'long', \n                  day: 'numeric' \n                })}\n              </p>\n            </div>\n          </div>\n          <div className=\"text-right\">\n            <div className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n              {currentTime.toLocaleTimeString('en-US', { \n                hour: '2-digit', \n                minute: '2-digit' \n              })}\n            </div>\n            <div className=\"flex items-center space-x-2 text-sm\">\n              <MapPin size={14} className=\"text-pink-500\" />\n              <span className={darkMode ? 'text-gray-300' : 'text-gray-600'}>Your Love Sanctuary</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Love Quote */}\n        <div className={`${\n          darkMode ? 'bg-gradient-to-r from-purple-900/50 to-pink-900/50' : 'bg-gradient-to-r from-pink-50 to-purple-50'\n        } rounded-xl p-4 text-center`}>\n          <div className=\"flex items-center justify-center space-x-2 mb-2\">\n            <Heart className=\"text-pink-500 animate-pulse\" size={20} />\n            <span className={`font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n              Daily Love Quote\n            </span>\n            <Heart className=\"text-pink-500 animate-pulse\" size={20} />\n          </div>\n          <p className={`text-lg font-semibold italic ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n            \"{quotes[currentQuote]}\"\n          </p>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div className={`${\n          darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n        } backdrop-blur-sm rounded-xl p-4 border text-center`}>\n          <div className=\"text-2xl mb-2\">💕</div>\n          <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>365+</div>\n          <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Days Together</div>\n        </div>\n        <div className={`${\n          darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n        } backdrop-blur-sm rounded-xl p-4 border text-center`}>\n          <div className=\"text-2xl mb-2\">💬</div>\n          <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>1,247</div>\n          <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Sweet Messages</div>\n        </div>\n        <div className={`${\n          darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n        } backdrop-blur-sm rounded-xl p-4 border text-center`}>\n          <div className=\"text-2xl mb-2\">📸</div>\n          <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>89</div>\n          <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Beautiful Memories</div>\n        </div>\n      </div>\n\n      {/* Personalized News Feed */}\n      <div className={`${\n        darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n      } backdrop-blur-sm rounded-2xl p-6 border`}>\n        <div className=\"flex items-center justify-between mb-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"text-2xl\">{isHisAccount ? '⚽' : '💄'}</div>\n            <div>\n              <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n                {isHisAccount ? 'Sports Updates' : 'Fashion & Beauty News'}\n              </h2>\n              <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>\n                {isHisAccount ? 'Latest from the sports world' : 'Trending in fashion and beauty'}\n              </p>\n            </div>\n          </div>\n          <button className={`p-2 rounded-lg ${\n            darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'\n          } transition-colors`}>\n            <RefreshCw size={18} className={`${newsLoading ? 'animate-spin' : ''} ${\n              darkMode ? 'text-gray-400' : 'text-gray-600'\n            }`} />\n          </button>\n        </div>\n\n        {newsLoading ? (\n          <div className=\"space-y-4\">\n            {[1, 2, 3].map(i => (\n              <div key={i} className={`${\n                darkMode ? 'bg-gray-700/50' : 'bg-gray-100'\n              } rounded-xl p-4 animate-pulse`}>\n                <div className=\"flex space-x-3\">\n                  <div className={`w-12 h-12 ${\n                    darkMode ? 'bg-gray-600' : 'bg-gray-200'\n                  } rounded-lg`}></div>\n                  <div className=\"flex-1 space-y-2\">\n                    <div className={`h-4 ${\n                      darkMode ? 'bg-gray-600' : 'bg-gray-200'\n                    } rounded w-3/4`}></div>\n                    <div className={`h-3 ${\n                      darkMode ? 'bg-gray-600' : 'bg-gray-200'\n                    } rounded w-1/2`}></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {(isHisAccount ? sportsNews : fashionNews).map(article => (\n              <NewsCard key={article.id} article={article} type={isHisAccount ? 'sports' : 'fashion'} />\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Today's Agenda */}\n      <div className={`${\n        darkMode ? 'bg-gray-800/70 border-gray-700' : 'bg-white/70 border-gray-200'\n      } backdrop-blur-sm rounded-2xl p-6 border`}>\n        <div className=\"flex items-center space-x-3 mb-4\">\n          <Calendar className=\"text-purple-500\" size={24} />\n          <h2 className={`text-xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`}>\n            Today's Love Agenda\n          </h2>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-pink-500/10 to-purple-500/10\">\n            <Coffee className=\"text-pink-500\" size={18} />\n            <span className={darkMode ? 'text-gray-300' : 'text-gray-700'}>\n              Morning coffee together ☕\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-blue-500/10 to-indigo-500/10\">\n            <Heart className=\"text-blue-500\" size={18} />\n            <span className={darkMode ? 'text-gray-300' : 'text-gray-700'}>\n              Share a sweet message 💌\n            </span>\n          </div>\n          <div className=\"flex items-center space-x-3 p-3 rounded-lg bg-gradient-to-r from-purple-500/10 to-pink-500/10\">\n            <Star className=\"text-purple-500\" size={18} />\n            <span className={darkMode ? 'text-gray-300' : 'text-gray-700'}>\n              Plan weekend date night 🌟\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,QAAQ,EAAEC,KAAK,EAAEC,aAAa,EAAEC,MAAM,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/E,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM;IAAEC;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEY;EAAS,CAAC,GAAGX,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,IAAIiB,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAMmB,KAAK,GAAGC,WAAW,CAAC,MAAML,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;IACjE,OAAO,MAAMK,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAENnB,SAAS,CAAC,MAAM;IAAA,IAAAsB,UAAA,EAAAC,WAAA;IACdL,eAAe,CAAC,CAAAN,IAAI,aAAJA,IAAI,wBAAAU,UAAA,GAAJV,IAAI,CAAEY,IAAI,cAAAF,UAAA,uBAAVA,UAAA,CAAYG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,MAAId,IAAI,aAAJA,IAAI,wBAAAW,WAAA,GAAJX,IAAI,CAAEe,KAAK,cAAAJ,WAAA,uBAAXA,WAAA,CAAaG,QAAQ,CAAC,MAAM,CAAC,EAAC;EAC9F,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC;EAEV,MAAMgB,QAAQ,GAAGA,CAAC;IAAEC,OAAO;IAAEC;EAAK,CAAC,kBACjCrB,OAAA;IAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,0GAC8B;IAAAmB,QAAA,eACzGvB,OAAA;MAAKsB,SAAS,EAAC,4BAA4B;MAAAC,QAAA,gBACzCvB,OAAA;QAAKsB,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAEH,OAAO,CAACI;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/C5B,OAAA;QAAKsB,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBvB,OAAA;UAAKsB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAC/CvB,OAAA;YAAMsB,SAAS,EAAE,8CACfD,IAAI,KAAK,QAAQ,GACb,kEAAkE,GAClE,kEAAkE,EACrE;YAAAE,QAAA,EACAH,OAAO,CAACS;UAAQ;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACP5B,OAAA;YAAMsB,SAAS,EAAE,WAAWlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;YAAAmB,QAAA,EACxEH,OAAO,CAACU;UAAI;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5B,OAAA;UAAIsB,SAAS,EAAE,kEACblB,QAAQ,GAAG,YAAY,GAAG,eAAe,EACxC;UAAAmB,QAAA,EACAH,OAAO,CAACW;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACL5B,OAAA;UAAGsB,SAAS,EAAE,gBAAgBlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAC1EH,OAAO,CAACY;QAAO;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACJ5B,OAAA;UAAKsB,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDvB,OAAA;YAAMsB,SAAS,EAAE,uBAAuBlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;YAAAmB,QAAA,EACpFH,OAAO,CAACa;UAAM;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACP5B,OAAA,CAACkC,YAAY;YAACC,IAAI,EAAE,EAAG;YAACb,SAAS,EAAE,GAAGlB,QAAQ,GAAG,eAAe,GAAG,eAAe;UAA+C;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,oBACE5B,OAAA;IAAKsB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExBvB,OAAA;MAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,0CAClC;MAAAmB,QAAA,gBACzCvB,OAAA;QAAKsB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDvB,OAAA;UAAKsB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CvB,OAAA;YAAKsB,SAAS,EAAC,6HAA6H;YAAAC,QAAA,EACzIf,YAAY,GAAG,OAAO,GAAG;UAAO;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACN5B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAE,sBAAsBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;cAAAmB,QAAA,GAAC,OAC3E,EAAClB,WAAW,CAAC+B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS,GAAG/B,WAAW,CAAC+B,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,WAAW,GAAG,SAAS,EAAC,IAAE,EAACjC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI,EAAC,GACpH,EAACP,YAAY,GAAG,KAAK,GAAG,IAAI;YAAA;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACL5B,OAAA;cAAGsB,SAAS,EAAE,GAAGlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;cAAAmB,QAAA,EAC7DlB,WAAW,CAACgC,kBAAkB,CAAC,OAAO,EAAE;gBACvCC,OAAO,EAAE,MAAM;gBACfC,IAAI,EAAE,SAAS;gBACfC,KAAK,EAAE,MAAM;gBACbC,GAAG,EAAE;cACP,CAAC;YAAC;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5B,OAAA;UAAKsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBvB,OAAA;YAAKsB,SAAS,EAAE,sBAAsBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;YAAAmB,QAAA,EAC/ElB,WAAW,CAACqC,kBAAkB,CAAC,OAAO,EAAE;cACvCC,IAAI,EAAE,SAAS;cACfC,MAAM,EAAE;YACV,CAAC;UAAC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN5B,OAAA;YAAKsB,SAAS,EAAC,qCAAqC;YAAAC,QAAA,gBAClDvB,OAAA,CAAC6C,MAAM;cAACV,IAAI,EAAE,EAAG;cAACb,SAAS,EAAC;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C5B,OAAA;cAAMsB,SAAS,EAAElB,QAAQ,GAAG,eAAe,GAAG,eAAgB;cAAAmB,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5B,OAAA;QAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,oDAAoD,GAAG,4CAA4C,6BAClF;QAAAmB,QAAA,gBAC5BvB,OAAA;UAAKsB,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DvB,OAAA,CAACL,KAAK;YAAC2B,SAAS,EAAC,6BAA6B;YAACa,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3D5B,OAAA;YAAMsB,SAAS,EAAE,eAAelB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;YAAAmB,QAAA,EAAC;UAEhF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACP5B,OAAA,CAACL,KAAK;YAAC2B,SAAS,EAAC,6BAA6B;YAACa,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACN5B,OAAA;UAAGsB,SAAS,EAAE,gCAAgClB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;UAAAmB,QAAA,GAAC,IACxF,EAACuB,MAAM,CAACC,YAAY,CAAC,EAAC,IACzB;QAAA;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKsB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDvB,OAAA;QAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,qDACvB;QAAAmB,QAAA,gBACpDvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC5B,OAAA;UAAKsB,SAAS,EAAE,sBAAsBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7F5B,OAAA;UAAKsB,SAAS,EAAE,WAAWlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAAa;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3F,CAAC,eACN5B,OAAA;QAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,qDACvB;QAAAmB,QAAA,gBACpDvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC5B,OAAA;UAAKsB,SAAS,EAAE,sBAAsBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC9F5B,OAAA;UAAKsB,SAAS,EAAE,WAAWlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAAc;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5F,CAAC,eACN5B,OAAA;QAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,qDACvB;QAAAmB,QAAA,gBACpDvB,OAAA;UAAKsB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC5B,OAAA;UAAKsB,SAAS,EAAE,sBAAsBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC3F5B,OAAA;UAAKsB,SAAS,EAAE,WAAWlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAAkB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,0CAClC;MAAAmB,QAAA,gBACzCvB,OAAA;QAAKsB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDvB,OAAA;UAAKsB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CvB,OAAA;YAAKsB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAEf,YAAY,GAAG,GAAG,GAAG;UAAI;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3D5B,OAAA;YAAAuB,QAAA,gBACEvB,OAAA;cAAIsB,SAAS,EAAE,qBAAqBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;cAAAmB,QAAA,EAC7Ef,YAAY,GAAG,gBAAgB,GAAG;YAAuB;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACL5B,OAAA;cAAGsB,SAAS,EAAE,WAAWlB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAAG;cAAAmB,QAAA,EACrEf,YAAY,GAAG,8BAA8B,GAAG;YAAgC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN5B,OAAA;UAAQsB,SAAS,EAAE,kBACjBlB,QAAQ,GAAG,mBAAmB,GAAG,mBAAmB,oBACjC;UAAAmB,QAAA,eACnBvB,OAAA,CAACgD,SAAS;YAACb,IAAI,EAAE,EAAG;YAACb,SAAS,EAAE,GAAG2B,WAAW,GAAG,cAAc,GAAG,EAAE,IAClE7C,QAAQ,GAAG,eAAe,GAAG,eAAe;UAC3C;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,EAELqB,WAAW,gBACVjD,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAACC,CAAC,iBACdnD,OAAA;UAAasB,SAAS,EAAE,GACtBlB,QAAQ,GAAG,gBAAgB,GAAG,aAAa,+BACb;UAAAmB,QAAA,eAC9BvB,OAAA;YAAKsB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BvB,OAAA;cAAKsB,SAAS,EAAE,aACdlB,QAAQ,GAAG,aAAa,GAAG,aAAa;YAC5B;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB5B,OAAA;cAAKsB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BvB,OAAA;gBAAKsB,SAAS,EAAE,OACdlB,QAAQ,GAAG,aAAa,GAAG,aAAa;cACzB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxB5B,OAAA;gBAAKsB,SAAS,EAAE,OACdlB,QAAQ,GAAG,aAAa,GAAG,aAAa;cACzB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAfEuB,CAAC;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBN,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,gBAEN5B,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,EACvB,CAACf,YAAY,GAAG4C,UAAU,GAAGC,WAAW,EAAEH,GAAG,CAAC9B,OAAO,iBACpDpB,OAAA,CAACmB,QAAQ;UAAkBC,OAAO,EAAEA,OAAQ;UAACC,IAAI,EAAEb,YAAY,GAAG,QAAQ,GAAG;QAAU,GAAxEY,OAAO,CAACkC,EAAE;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgE,CAC1F;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN5B,OAAA;MAAKsB,SAAS,EAAE,GACdlB,QAAQ,GAAG,gCAAgC,GAAG,6BAA6B,0CAClC;MAAAmB,QAAA,gBACzCvB,OAAA;QAAKsB,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CvB,OAAA,CAACN,QAAQ;UAAC4B,SAAS,EAAC,iBAAiB;UAACa,IAAI,EAAE;QAAG;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClD5B,OAAA;UAAIsB,SAAS,EAAE,qBAAqBlB,QAAQ,GAAG,YAAY,GAAG,eAAe,EAAG;UAAAmB,QAAA,EAAC;QAEjF;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACN5B,OAAA;QAAKsB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBvB,OAAA;UAAKsB,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAC5GvB,OAAA,CAACuD,MAAM;YAACjC,SAAS,EAAC,eAAe;YAACa,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9C5B,OAAA;YAAMsB,SAAS,EAAElB,QAAQ,GAAG,eAAe,GAAG,eAAgB;YAAAmB,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5B,OAAA;UAAKsB,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAC5GvB,OAAA,CAACL,KAAK;YAAC2B,SAAS,EAAC,eAAe;YAACa,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7C5B,OAAA;YAAMsB,SAAS,EAAElB,QAAQ,GAAG,eAAe,GAAG,eAAgB;YAAAmB,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACN5B,OAAA;UAAKsB,SAAS,EAAC,+FAA+F;UAAAC,QAAA,gBAC5GvB,OAAA,CAACwD,IAAI;YAAClC,SAAS,EAAC,iBAAiB;YAACa,IAAI,EAAE;UAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9C5B,OAAA;YAAMsB,SAAS,EAAElB,QAAQ,GAAG,eAAe,GAAG,eAAgB;YAAAmB,QAAA,EAAC;UAE/D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1B,EAAA,CAhOID,IAAI;EAAA,QACST,OAAO,EACHC,QAAQ;AAAA;AAAAgE,EAAA,GAFzBxD,IAAI;AAkOV,eAAeA,IAAI;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
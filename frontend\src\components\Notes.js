import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { Plus, Heart, Edit3, Trash2, Save, X, Calendar, Tag, Search, Filter, PenTool, User, StickyNote } from 'lucide-react';

// Mock API for notes
const notesAPI = {
  getNotes: () => Promise.resolve([
    {
      id: 1,
      title: "Our First Date",
      content: "Remember when we went to that little café downtown? You ordered a vanilla latte and I was so nervous I forgot my own order! 😅",
      date: "2024-01-15",
      tags: ["memories", "first-date"],
      color: "yellow",
      author: "<PERSON>"
    },
    {
      id: 2,
      title: "Things I Love About You",
      content: "Your laugh, the way you scrunch your nose when you're thinking, how you always know exactly what to say...",
      date: "2024-01-20",
      tags: ["love", "appreciation"],
      color: "pink",
      author: "<PERSON>"
    }
  ]),
  createNote: (note) => Promise.resolve({ ...note, id: Date.now() }),
  updateNote: (id, note) => Promise.resolve({ ...note, id }),
  deleteNote: (id) => Promise.resolve(true)
};

const Notes = () => {
  const { user } = useAuth();
  const { darkMode } = useTheme();
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [filterBy, setFilterBy] = useState('all');
  const [newNote, setNewNote] = useState({
    title: '',
    content: '',
    tags: [],
    color: 'yellow'
  });

  useEffect(() => {
    loadNotes();
  }, []);

  const loadNotes = async () => {
    try {
      const response = await notesAPI.getNotes();
      setNotes(response || []); // Fix: handle undefined response
      setLoading(false);
    } catch (error) {
      console.error('Error loading notes:', error);
      setNotes([]); // Fix: set empty array on error
      setLoading(false);
    }
  };

  const createNote = async (e) => {
    e.preventDefault();
    if (!newNote.content.trim()) return;

    try {
      const noteToCreate = {
        ...newNote,
        author: user?.name || 'Anonymous',
        author_name: user?.name || 'Anonymous',
        user_id: user?.id || 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      const response = await notesAPI.createNote(noteToCreate);
      setNotes(prev => [response, ...prev]); // Fix: use response directly
      setNewNote({
        title: '',
        content: '',
        tags: [],
        color: 'yellow'
      });
      setShowCreateForm(false);
    } catch (error) {
      console.error('Error creating note:', error);
      alert('Error creating note');
    }
  };

  const updateNote = async (noteId, content) => {
    try {
      const updatedNote = {
        ...notes.find(n => n.id === noteId),
        content,
        updated_at: new Date().toISOString()
      };
      const response = await notesAPI.updateNote(noteId, updatedNote);
      setNotes(prev => prev.map(note => 
        note.id === noteId ? response : note
      ));
      setEditingNote(null);
    } catch (error) {
      console.error('Error updating note:', error);
      alert('Error updating note');
    }
  };

  const deleteNote = async (noteId) => {
    if (!window.confirm('Are you sure you want to delete this note?')) return;

    try {
      await notesAPI.deleteNote(noteId);
      setNotes(prev => prev.filter(note => note.id !== noteId));
    } catch (error) {
      console.error('Error deleting note:', error);
      alert('Error deleting note');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      createNote(e);
    }
  };

  // Filter and search notes
  const filteredNotes = notes.filter(note => {
    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         note.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterBy === 'all' || 
                         (filterBy === 'mine' && note.author === user?.name) ||
                         (filterBy === 'theirs' && note.author !== user?.name);
    return matchesSearch && matchesFilter;
  });

  // Note colors for variety
  const noteColors = [
    'from-yellow-200 to-yellow-300 border-yellow-400',
    'from-pink-200 to-pink-300 border-pink-400',
    'from-blue-200 to-blue-300 border-blue-400',
    'from-green-200 to-green-300 border-green-400',
    'from-purple-200 to-purple-300 border-purple-400',
    'from-orange-200 to-orange-300 border-orange-400',
    'from-red-200 to-red-300 border-red-400',
    'from-indigo-200 to-indigo-300 border-indigo-400',
  ];

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4"></div>
        <p className="text-pink-600 font-medium flex items-center justify-center gap-2">
          <StickyNote className="animate-pulse" size={20} />
          Loading our love notes...
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Create Note Section */}
      <div className="bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6 relative overflow-hidden">
        {/* Decorative elements */}
        <div className="absolute top-2 right-2 text-2xl opacity-20 animate-pulse">💕</div>
        <div className="absolute bottom-2 left-2 text-xl opacity-20 animate-bounce">✨</div>
        
        <div className="flex items-center gap-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg">
            <PenTool className="text-white" size={20} />
          </div>
          <h3 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            Write a Love Note
            <Heart className="text-red-500 animate-pulse" size={20} />
          </h3>
        </div>
        
        <form onSubmit={createNote} className="space-y-4">
          <div className="relative">
            <textarea
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Pour your heart out... Write something beautiful for us 💖"
              rows={4}
              className="w-full px-4 py-3 border-2 border-pink-300 rounded-xl focus:outline-none focus:ring-4 focus:ring-pink-200 focus:border-pink-500 resize-none transition-all duration-300 bg-white/80 backdrop-blur-sm text-gray-800 placeholder-pink-400"
            />
            <div className="absolute bottom-3 right-3 text-pink-400">
              <StickyNote size={20} />
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <button
              type="submit"
              disabled={!newNote.trim()}
              className="px-6 py-3 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 focus:outline-none focus:ring-4 focus:ring-pink-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium flex items-center gap-2 shadow-lg hover:shadow-xl hover:scale-105"
            >
              <Plus size={18} />
              Add Love Note
            </button>
            <p className="text-sm text-gray-500 flex items-center gap-1">
              <span>Press Enter to add note, Shift+Enter for new line</span>
            </p>
          </div>
        </form>
      </div>

      {/* Search and Filter Section */}
      <div className="flex flex-wrap gap-4 items-center justify-between bg-white rounded-xl p-4 shadow-md border border-pink-100">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-pink-400" size={18} />
            <input
              type="text"
              placeholder="Search notes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 focus:border-pink-400 bg-pink-50/50"
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Filter className="text-pink-500" size={18} />
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value)}
              className="px-3 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 bg-white"
            >
              <option value="all">All Notes</option>
              <option value="mine">My Notes</option>
              <option value="theirs">Their Notes</option>
            </select>
          </div>
        </div>
        
        <div className="text-sm text-pink-600 font-medium flex items-center gap-1">
          <StickyNote size={16} />
          {filteredNotes.length} love notes
        </div>
      </div>

      {/* Notes Grid */}
      <div className="bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
            <Heart className="text-white" size={20} />
          </div>
          <h3 className="text-2xl font-bold text-gray-800">Our Love Notes Collection</h3>
        </div>
        
        {filteredNotes.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">📝</div>
            <h4 className="text-xl font-bold text-gray-600 mb-2">
              {searchTerm ? 'No notes found' : 'No notes yet'}
            </h4>
            <p className="text-gray-500 mb-6">
              {searchTerm ? 'Try a different search term' : 'Write your first love note! ✍️'}
            </p>
            <div className="flex justify-center space-x-3 text-2xl">
              <span className="animate-bounce" style={{animationDelay: '0s'}}>💕</span>
              <span className="animate-bounce" style={{animationDelay: '0.2s'}}>📝</span>
              <span className="animate-bounce" style={{animationDelay: '0.4s'}}>💖</span>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredNotes.map((note, index) => (
              <NoteCard
                key={note.id}
                note={note}
                colorClass={noteColors[index % noteColors.length]}
                isOwner={note.user_id === user.id}
                isEditing={editingNote === note.id}
                onEdit={() => setEditingNote(note.id)}
                onCancelEdit={() => setEditingNote(null)}
                onSave={(content) => updateNote(note.id, content)}
                onDelete={() => deleteNote(note.id)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Individual Note Card Component
const NoteCard = ({ note, colorClass, isOwner, isEditing, onEdit, onCancelEdit, onSave, onDelete }) => {
  const [editContent, setEditContent] = useState(note.content);

  const handleSave = () => {
    if (editContent.trim()) {
      onSave(editContent.trim());
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      handleSave();
    }
    if (e.key === 'Escape') {
      onCancelEdit();
      setEditContent(note.content);
    }
  };

  return (
    <div className={`bg-gradient-to-br ${colorClass} rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:rotate-1 border-2 relative group min-h-[200px] flex flex-col`}>
      {/* Decorative corner */}
      <div className="absolute top-2 right-2 text-lg opacity-30 group-hover:opacity-60 transition-opacity">
        {isOwner ? '💝' : '💕'}
      </div>
      
      {/* Note content */}
      <div className="flex-1 mb-4">
        {isEditing ? (
          <textarea
            value={editContent}
            onChange={(e) => setEditContent(e.target.value)}
            onKeyPress={handleKeyPress}
            className="w-full h-32 p-3 bg-white/70 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-gray-800"
            autoFocus
          />
        ) : (
          <p className="text-gray-800 whitespace-pre-wrap leading-relaxed text-sm font-medium">
            {note.content}
          </p>
        )}
      </div>
      
      {/* Note metadata */}
      <div className="border-t border-white/50 pt-3 mt-auto">
        <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
          <div className="flex items-center gap-1">
            <User size={12} />
            <span className="font-medium">{note.author_name}</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar size={12} />
            <span>{new Date(note.created_at).toLocaleDateString()}</span>
          </div>
        </div>
        
        {note.updated_at !== note.created_at && (
          <div className="text-xs text-gray-500 mb-2 flex items-center gap-1">
            <Edit3 size={10} />
            <span>Edited {new Date(note.updated_at).toLocaleDateString()}</span>
          </div>
        )}
        
        {/* Action buttons */}
        {isOwner && (
          <div className="flex justify-end space-x-2 mt-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  className="p-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center gap-1 text-xs"
                >
                  <Save size={12} />
                  Save
                </button>
                <button
                  onClick={onCancelEdit}
                  className="p-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center gap-1 text-xs"
                >
                  <X size={12} />
                  Cancel
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={onEdit}
                  className="p-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs"
                >
                  <Edit3 size={12} />
                  Edit
                </button>
                <button
                  onClick={onDelete}
                  className="p-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs"
                >
                  <Trash2 size={12} />
                  Delete
                </button>
              </>
            )}
          </div>
        )}
        
        {isEditing && (
          <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
            <span>Ctrl+Enter to save, Escape to cancel</span>
          </p>
        )}
      </div>
    </div>
  );
};

export default Notes;








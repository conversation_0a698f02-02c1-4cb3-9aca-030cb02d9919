{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Notes.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Plus, Heart, Edit3, Trash2, Save, X, Calendar, Tag, Search, Filter, PenTool, User, StickyNote } from 'lucide-react';\n\n// Mock API for notes\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst notesAPI = {\n  getNotes: () => Promise.resolve([{\n    id: 1,\n    title: \"Our First Date\",\n    content: \"Remember when we went to that little café downtown? You ordered a vanilla latte and I was so nervous I forgot my own order! 😅\",\n    date: \"2024-01-15\",\n    tags: [\"memories\", \"first-date\"],\n    color: \"yellow\",\n    author: \"<PERSON>\"\n  }, {\n    id: 2,\n    title: \"Things I Love About You\",\n    content: \"Your laugh, the way you scrunch your nose when you're thinking, how you always know exactly what to say...\",\n    date: \"2024-01-20\",\n    tags: [\"love\", \"appreciation\"],\n    color: \"pink\",\n    author: \"Sarah\"\n  }]),\n  createNote: note => Promise.resolve({\n    ...note,\n    id: Date.now()\n  }),\n  updateNote: (id, note) => Promise.resolve({\n    ...note,\n    id\n  }),\n  deleteNote: id => Promise.resolve(true)\n};\nconst Notes = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n  const [notes, setNotes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedTag, setSelectedTag] = useState('');\n  const [filterBy, setFilterBy] = useState('all');\n  const [newNote, setNewNote] = useState({\n    title: '',\n    content: '',\n    tags: [],\n    color: 'yellow'\n  });\n  useEffect(() => {\n    loadNotes();\n  }, []);\n  const loadNotes = async () => {\n    try {\n      const response = await notesAPI.getNotes();\n      setNotes(response || []); // Fix: handle undefined response\n      setLoading(false);\n    } catch (error) {\n      console.error('Error loading notes:', error);\n      setNotes([]); // Fix: set empty array on error\n      setLoading(false);\n    }\n  };\n  const createNote = async e => {\n    e.preventDefault();\n    if (!newNote.content.trim()) return;\n    try {\n      const noteToCreate = {\n        ...newNote,\n        author: (user === null || user === void 0 ? void 0 : user.name) || 'Anonymous',\n        author_name: (user === null || user === void 0 ? void 0 : user.name) || 'Anonymous',\n        user_id: (user === null || user === void 0 ? void 0 : user.id) || 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.createNote(noteToCreate);\n      setNotes(prev => [response, ...prev]); // Fix: use response directly\n      setNewNote({\n        title: '',\n        content: '',\n        tags: [],\n        color: 'yellow'\n      });\n      setShowCreateForm(false);\n    } catch (error) {\n      console.error('Error creating note:', error);\n      alert('Error creating note');\n    }\n  };\n  const updateNote = async (noteId, content) => {\n    try {\n      const updatedNote = {\n        ...notes.find(n => n.id === noteId),\n        content,\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.updateNote(noteId, updatedNote);\n      setNotes(prev => prev.map(note => note.id === noteId ? response : note));\n      setEditingNote(null);\n    } catch (error) {\n      console.error('Error updating note:', error);\n      alert('Error updating note');\n    }\n  };\n  const deleteNote = async noteId => {\n    if (!window.confirm('Are you sure you want to delete this note?')) return;\n    try {\n      await notesAPI.deleteNote(noteId);\n      setNotes(prev => prev.filter(note => note.id !== noteId));\n    } catch (error) {\n      console.error('Error deleting note:', error);\n      alert('Error deleting note');\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      createNote(e);\n    }\n  };\n\n  // Filter and search notes\n  const filteredNotes = notes.filter(note => {\n    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) || note.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterBy === 'all' || filterBy === 'mine' && note.author === (user === null || user === void 0 ? void 0 : user.name) || filterBy === 'theirs' && note.author !== (user === null || user === void 0 ? void 0 : user.name);\n    return matchesSearch && matchesFilter;\n  });\n\n  // Note colors for variety\n  const noteColors = ['from-yellow-200 to-yellow-300 border-yellow-400', 'from-pink-200 to-pink-300 border-pink-400', 'from-blue-200 to-blue-300 border-blue-400', 'from-green-200 to-green-300 border-green-400', 'from-purple-200 to-purple-300 border-purple-400', 'from-orange-200 to-orange-300 border-orange-400', 'from-red-200 to-red-300 border-red-400', 'from-indigo-200 to-indigo-300 border-indigo-400'];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-center py-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-pink-600 font-medium flex items-center justify-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(StickyNote, {\n          className: \"animate-pulse\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), \"Loading our love notes...\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6 relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-2 right-2 text-2xl opacity-20 animate-pulse\",\n        children: \"\\uD83D\\uDC95\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-2 left-2 text-xl opacity-20 animate-bounce\",\n        children: \"\\u2728\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(PenTool, {\n            className: \"text-white\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-800 flex items-center gap-2\",\n          children: [\"Write a Love Note\", /*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-red-500 animate-pulse\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: createNote,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: newNote,\n            onChange: e => setNewNote(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Pour your heart out... Write something beautiful for us \\uD83D\\uDC96\",\n            rows: 4,\n            className: \"w-full px-4 py-3 border-2 border-pink-300 rounded-xl focus:outline-none focus:ring-4 focus:ring-pink-200 focus:border-pink-500 resize-none transition-all duration-300 bg-white/80 backdrop-blur-sm text-gray-800 placeholder-pink-400\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute bottom-3 right-3 text-pink-400\",\n            children: /*#__PURE__*/_jsxDEV(StickyNote, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: !newNote.trim(),\n            className: \"px-6 py-3 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 focus:outline-none focus:ring-4 focus:ring-pink-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium flex items-center gap-2 shadow-lg hover:shadow-xl hover:scale-105\",\n            children: [/*#__PURE__*/_jsxDEV(Plus, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), \"Add Love Note\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 flex items-center gap-1\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Press Enter to add note, Shift+Enter for new line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-4 items-center justify-between bg-white rounded-xl p-4 shadow-md border border-pink-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(Search, {\n            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-pink-400\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search notes...\",\n            value: searchTerm,\n            onChange: e => setSearchTerm(e.target.value),\n            className: \"pl-10 pr-4 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 focus:border-pink-400 bg-pink-50/50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Filter, {\n            className: \"text-pink-500\",\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: filterBy,\n            onChange: e => setFilterBy(e.target.value),\n            className: \"px-3 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 bg-white\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"all\",\n              children: \"All Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"mine\",\n              children: \"My Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"theirs\",\n              children: \"Their Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-sm text-pink-600 font-medium flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(StickyNote, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 11\n        }, this), filteredNotes.length, \" love notes\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 215,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(Heart, {\n            className: \"text-white\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-800\",\n          children: \"Our Love Notes Collection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), filteredNotes.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-xl font-bold text-gray-600 mb-2\",\n          children: searchTerm ? 'No notes found' : 'No notes yet'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500 mb-6\",\n          children: searchTerm ? 'Try a different search term' : 'Write your first love note! ✍️'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-3 text-2xl\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-bounce\",\n            style: {\n              animationDelay: '0s'\n            },\n            children: \"\\uD83D\\uDC95\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-bounce\",\n            style: {\n              animationDelay: '0.2s'\n            },\n            children: \"\\uD83D\\uDCDD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"animate-bounce\",\n            style: {\n              animationDelay: '0.4s'\n            },\n            children: \"\\uD83D\\uDC96\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: filteredNotes.map((note, index) => /*#__PURE__*/_jsxDEV(NoteCard, {\n          note: note,\n          colorClass: noteColors[index % noteColors.length],\n          isOwner: note.user_id === user.id,\n          isEditing: editingNote === note.id,\n          onEdit: () => setEditingNote(note.id),\n          onCancelEdit: () => setEditingNote(null),\n          onSave: content => updateNote(note.id, content),\n          onDelete: () => deleteNote(note.id)\n        }, note.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n\n// Individual Note Card Component\n_s(Notes, \"cGY4b2kj1U5HMRRp/5dSHyQCsnU=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = Notes;\nconst NoteCard = ({\n  note,\n  colorClass,\n  isOwner,\n  isEditing,\n  onEdit,\n  onCancelEdit,\n  onSave,\n  onDelete\n}) => {\n  _s2();\n  const [editContent, setEditContent] = useState(note.content);\n  const handleSave = () => {\n    if (editContent.trim()) {\n      onSave(editContent.trim());\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && e.ctrlKey) {\n      e.preventDefault();\n      handleSave();\n    }\n    if (e.key === 'Escape') {\n      onCancelEdit();\n      setEditContent(note.content);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `bg-gradient-to-br ${colorClass} rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:rotate-1 border-2 relative group min-h-[200px] flex flex-col`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-2 right-2 text-lg opacity-30 group-hover:opacity-60 transition-opacity\",\n      children: isOwner ? '💝' : '💕'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 mb-4\",\n      children: isEditing ? /*#__PURE__*/_jsxDEV(\"textarea\", {\n        value: editContent,\n        onChange: e => setEditContent(e.target.value),\n        onKeyPress: handleKeyPress,\n        className: \"w-full h-32 p-3 bg-white/70 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-gray-800\",\n        autoFocus: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-800 whitespace-pre-wrap leading-relaxed text-sm font-medium\",\n        children: note.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"border-t border-white/50 pt-3 mt-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between text-xs text-gray-600 mb-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: note.author_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-1\",\n          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n            size: 12\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: new Date(note.created_at).toLocaleDateString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 9\n      }, this), note.updated_at !== note.created_at && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-500 mb-2 flex items-center gap-1\",\n        children: [/*#__PURE__*/_jsxDEV(Edit3, {\n          size: 10\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Edited \", new Date(note.updated_at).toLocaleDateString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 11\n      }, this), isOwner && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-end space-x-2 mt-2\",\n        children: isEditing ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleSave,\n            className: \"p-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(Save, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this), \"Save\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onCancelEdit,\n            className: \"p-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(X, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 19\n            }, this), \"Cancel\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onEdit,\n            className: \"p-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(Edit3, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 19\n            }, this), \"Edit\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onDelete,\n            className: \"p-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\",\n            children: [/*#__PURE__*/_jsxDEV(Trash2, {\n              size: 12\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 19\n            }, this), \"Delete\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this), isEditing && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-500 mt-2 flex items-center gap-1\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Ctrl+Enter to save, Escape to cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 401,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 316,\n    columnNumber: 5\n  }, this);\n};\n_s2(NoteCard, \"Vu+w5YaFRxoQaV5Fqo2PSfof6gE=\");\n_c2 = NoteCard;\nexport default Notes;\nvar _c, _c2;\n$RefreshReg$(_c, \"Notes\");\n$RefreshReg$(_c2, \"NoteCard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useAuth", "useTheme", "Plus", "Heart", "Edit3", "Trash2", "Save", "X", "Calendar", "Tag", "Search", "Filter", "PenTool", "User", "StickyNote", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "notesAPI", "getNotes", "Promise", "resolve", "id", "title", "content", "date", "tags", "color", "author", "createNote", "note", "Date", "now", "updateNote", "deleteNote", "Notes", "_s", "user", "darkMode", "notes", "setNotes", "loading", "setLoading", "showCreateForm", "setShowCreateForm", "editingNote", "setEditingNote", "searchTerm", "setSearchTerm", "selectedTag", "setSelectedTag", "filterBy", "setFilterBy", "newNote", "setNewNote", "loadNotes", "response", "error", "console", "e", "preventDefault", "trim", "noteToCreate", "name", "author_name", "user_id", "created_at", "toISOString", "updated_at", "prev", "alert", "noteId", "updatedNote", "find", "n", "map", "window", "confirm", "filter", "handleKeyPress", "key", "shift<PERSON>ey", "filteredNotes", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "noteColors", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "onSubmit", "value", "onChange", "target", "onKeyPress", "placeholder", "rows", "type", "disabled", "length", "style", "animationDelay", "index", "NoteCard", "colorClass", "isOwner", "isEditing", "onEdit", "onCancelEdit", "onSave", "onDelete", "_c", "_s2", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSave", "ctrl<PERSON>ey", "autoFocus", "toLocaleDateString", "onClick", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Notes.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Plus, Heart, Edit3, Trash2, Save, X, Calendar, Tag, Search, Filter, PenTool, User, StickyNote } from 'lucide-react';\n\n// Mock API for notes\nconst notesAPI = {\n  getNotes: () => Promise.resolve([\n    {\n      id: 1,\n      title: \"Our First Date\",\n      content: \"Remember when we went to that little café downtown? You ordered a vanilla latte and I was so nervous I forgot my own order! 😅\",\n      date: \"2024-01-15\",\n      tags: [\"memories\", \"first-date\"],\n      color: \"yellow\",\n      author: \"<PERSON>\"\n    },\n    {\n      id: 2,\n      title: \"Things I Love About You\",\n      content: \"Your laugh, the way you scrunch your nose when you're thinking, how you always know exactly what to say...\",\n      date: \"2024-01-20\",\n      tags: [\"love\", \"appreciation\"],\n      color: \"pink\",\n      author: \"<PERSON>\"\n    }\n  ]),\n  createNote: (note) => Promise.resolve({ ...note, id: Date.now() }),\n  updateNote: (id, note) => Promise.resolve({ ...note, id }),\n  deleteNote: (id) => Promise.resolve(true)\n};\n\nconst Notes = () => {\n  const { user } = useAuth();\n  const { darkMode } = useTheme();\n  const [notes, setNotes] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [showCreateForm, setShowCreateForm] = useState(false);\n  const [editingNote, setEditingNote] = useState(null);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedTag, setSelectedTag] = useState('');\n  const [filterBy, setFilterBy] = useState('all');\n  const [newNote, setNewNote] = useState({\n    title: '',\n    content: '',\n    tags: [],\n    color: 'yellow'\n  });\n\n  useEffect(() => {\n    loadNotes();\n  }, []);\n\n  const loadNotes = async () => {\n    try {\n      const response = await notesAPI.getNotes();\n      setNotes(response || []); // Fix: handle undefined response\n      setLoading(false);\n    } catch (error) {\n      console.error('Error loading notes:', error);\n      setNotes([]); // Fix: set empty array on error\n      setLoading(false);\n    }\n  };\n\n  const createNote = async (e) => {\n    e.preventDefault();\n    if (!newNote.content.trim()) return;\n\n    try {\n      const noteToCreate = {\n        ...newNote,\n        author: user?.name || 'Anonymous',\n        author_name: user?.name || 'Anonymous',\n        user_id: user?.id || 1,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.createNote(noteToCreate);\n      setNotes(prev => [response, ...prev]); // Fix: use response directly\n      setNewNote({\n        title: '',\n        content: '',\n        tags: [],\n        color: 'yellow'\n      });\n      setShowCreateForm(false);\n    } catch (error) {\n      console.error('Error creating note:', error);\n      alert('Error creating note');\n    }\n  };\n\n  const updateNote = async (noteId, content) => {\n    try {\n      const updatedNote = {\n        ...notes.find(n => n.id === noteId),\n        content,\n        updated_at: new Date().toISOString()\n      };\n      const response = await notesAPI.updateNote(noteId, updatedNote);\n      setNotes(prev => prev.map(note => \n        note.id === noteId ? response : note\n      ));\n      setEditingNote(null);\n    } catch (error) {\n      console.error('Error updating note:', error);\n      alert('Error updating note');\n    }\n  };\n\n  const deleteNote = async (noteId) => {\n    if (!window.confirm('Are you sure you want to delete this note?')) return;\n\n    try {\n      await notesAPI.deleteNote(noteId);\n      setNotes(prev => prev.filter(note => note.id !== noteId));\n    } catch (error) {\n      console.error('Error deleting note:', error);\n      alert('Error deleting note');\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      createNote(e);\n    }\n  };\n\n  // Filter and search notes\n  const filteredNotes = notes.filter(note => {\n    const matchesSearch = note.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         note.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesFilter = filterBy === 'all' || \n                         (filterBy === 'mine' && note.author === user?.name) ||\n                         (filterBy === 'theirs' && note.author !== user?.name);\n    return matchesSearch && matchesFilter;\n  });\n\n  // Note colors for variety\n  const noteColors = [\n    'from-yellow-200 to-yellow-300 border-yellow-400',\n    'from-pink-200 to-pink-300 border-pink-400',\n    'from-blue-200 to-blue-300 border-blue-400',\n    'from-green-200 to-green-300 border-green-400',\n    'from-purple-200 to-purple-300 border-purple-400',\n    'from-orange-200 to-orange-300 border-orange-400',\n    'from-red-200 to-red-300 border-red-400',\n    'from-indigo-200 to-indigo-300 border-indigo-400',\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-pink-500 mx-auto mb-4\"></div>\n        <p className=\"text-pink-600 font-medium flex items-center justify-center gap-2\">\n          <StickyNote className=\"animate-pulse\" size={20} />\n          Loading our love notes...\n        </p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Create Note Section */}\n      <div className=\"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6 relative overflow-hidden\">\n        {/* Decorative elements */}\n        <div className=\"absolute top-2 right-2 text-2xl opacity-20 animate-pulse\">💕</div>\n        <div className=\"absolute bottom-2 left-2 text-xl opacity-20 animate-bounce\">✨</div>\n        \n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg\">\n            <PenTool className=\"text-white\" size={20} />\n          </div>\n          <h3 className=\"text-2xl font-bold text-gray-800 flex items-center gap-2\">\n            Write a Love Note\n            <Heart className=\"text-red-500 animate-pulse\" size={20} />\n          </h3>\n        </div>\n        \n        <form onSubmit={createNote} className=\"space-y-4\">\n          <div className=\"relative\">\n            <textarea\n              value={newNote}\n              onChange={(e) => setNewNote(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Pour your heart out... Write something beautiful for us 💖\"\n              rows={4}\n              className=\"w-full px-4 py-3 border-2 border-pink-300 rounded-xl focus:outline-none focus:ring-4 focus:ring-pink-200 focus:border-pink-500 resize-none transition-all duration-300 bg-white/80 backdrop-blur-sm text-gray-800 placeholder-pink-400\"\n            />\n            <div className=\"absolute bottom-3 right-3 text-pink-400\">\n              <StickyNote size={20} />\n            </div>\n          </div>\n          \n          <div className=\"flex items-center justify-between\">\n            <button\n              type=\"submit\"\n              disabled={!newNote.trim()}\n              className=\"px-6 py-3 bg-gradient-to-r from-pink-500 to-rose-500 text-white rounded-xl hover:from-pink-600 hover:to-rose-600 focus:outline-none focus:ring-4 focus:ring-pink-200 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 font-medium flex items-center gap-2 shadow-lg hover:shadow-xl hover:scale-105\"\n            >\n              <Plus size={18} />\n              Add Love Note\n            </button>\n            <p className=\"text-sm text-gray-500 flex items-center gap-1\">\n              <span>Press Enter to add note, Shift+Enter for new line</span>\n            </p>\n          </div>\n        </form>\n      </div>\n\n      {/* Search and Filter Section */}\n      <div className=\"flex flex-wrap gap-4 items-center justify-between bg-white rounded-xl p-4 shadow-md border border-pink-100\">\n        <div className=\"flex items-center gap-4\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-pink-400\" size={18} />\n            <input\n              type=\"text\"\n              placeholder=\"Search notes...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-10 pr-4 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 focus:border-pink-400 bg-pink-50/50\"\n            />\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Filter className=\"text-pink-500\" size={18} />\n            <select\n              value={filterBy}\n              onChange={(e) => setFilterBy(e.target.value)}\n              className=\"px-3 py-2 border border-pink-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-300 bg-white\"\n            >\n              <option value=\"all\">All Notes</option>\n              <option value=\"mine\">My Notes</option>\n              <option value=\"theirs\">Their Notes</option>\n            </select>\n          </div>\n        </div>\n        \n        <div className=\"text-sm text-pink-600 font-medium flex items-center gap-1\">\n          <StickyNote size={16} />\n          {filteredNotes.length} love notes\n        </div>\n      </div>\n\n      {/* Notes Grid */}\n      <div className=\"bg-gradient-to-br from-white via-pink-50 to-rose-50 rounded-2xl shadow-lg border-2 border-pink-200 p-6\">\n        <div className=\"flex items-center gap-3 mb-6\">\n          <div className=\"w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg\">\n            <Heart className=\"text-white\" size={20} />\n          </div>\n          <h3 className=\"text-2xl font-bold text-gray-800\">Our Love Notes Collection</h3>\n        </div>\n        \n        {filteredNotes.length === 0 ? (\n          <div className=\"text-center py-16\">\n            <div className=\"text-6xl mb-4\">📝</div>\n            <h4 className=\"text-xl font-bold text-gray-600 mb-2\">\n              {searchTerm ? 'No notes found' : 'No notes yet'}\n            </h4>\n            <p className=\"text-gray-500 mb-6\">\n              {searchTerm ? 'Try a different search term' : 'Write your first love note! ✍️'}\n            </p>\n            <div className=\"flex justify-center space-x-3 text-2xl\">\n              <span className=\"animate-bounce\" style={{animationDelay: '0s'}}>💕</span>\n              <span className=\"animate-bounce\" style={{animationDelay: '0.2s'}}>📝</span>\n              <span className=\"animate-bounce\" style={{animationDelay: '0.4s'}}>💖</span>\n            </div>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredNotes.map((note, index) => (\n              <NoteCard\n                key={note.id}\n                note={note}\n                colorClass={noteColors[index % noteColors.length]}\n                isOwner={note.user_id === user.id}\n                isEditing={editingNote === note.id}\n                onEdit={() => setEditingNote(note.id)}\n                onCancelEdit={() => setEditingNote(null)}\n                onSave={(content) => updateNote(note.id, content)}\n                onDelete={() => deleteNote(note.id)}\n              />\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// Individual Note Card Component\nconst NoteCard = ({ note, colorClass, isOwner, isEditing, onEdit, onCancelEdit, onSave, onDelete }) => {\n  const [editContent, setEditContent] = useState(note.content);\n\n  const handleSave = () => {\n    if (editContent.trim()) {\n      onSave(editContent.trim());\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && e.ctrlKey) {\n      e.preventDefault();\n      handleSave();\n    }\n    if (e.key === 'Escape') {\n      onCancelEdit();\n      setEditContent(note.content);\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br ${colorClass} rounded-xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:rotate-1 border-2 relative group min-h-[200px] flex flex-col`}>\n      {/* Decorative corner */}\n      <div className=\"absolute top-2 right-2 text-lg opacity-30 group-hover:opacity-60 transition-opacity\">\n        {isOwner ? '💝' : '💕'}\n      </div>\n      \n      {/* Note content */}\n      <div className=\"flex-1 mb-4\">\n        {isEditing ? (\n          <textarea\n            value={editContent}\n            onChange={(e) => setEditContent(e.target.value)}\n            onKeyPress={handleKeyPress}\n            className=\"w-full h-32 p-3 bg-white/70 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-400 resize-none text-gray-800\"\n            autoFocus\n          />\n        ) : (\n          <p className=\"text-gray-800 whitespace-pre-wrap leading-relaxed text-sm font-medium\">\n            {note.content}\n          </p>\n        )}\n      </div>\n      \n      {/* Note metadata */}\n      <div className=\"border-t border-white/50 pt-3 mt-auto\">\n        <div className=\"flex items-center justify-between text-xs text-gray-600 mb-2\">\n          <div className=\"flex items-center gap-1\">\n            <User size={12} />\n            <span className=\"font-medium\">{note.author_name}</span>\n          </div>\n          <div className=\"flex items-center gap-1\">\n            <Calendar size={12} />\n            <span>{new Date(note.created_at).toLocaleDateString()}</span>\n          </div>\n        </div>\n        \n        {note.updated_at !== note.created_at && (\n          <div className=\"text-xs text-gray-500 mb-2 flex items-center gap-1\">\n            <Edit3 size={10} />\n            <span>Edited {new Date(note.updated_at).toLocaleDateString()}</span>\n          </div>\n        )}\n        \n        {/* Action buttons */}\n        {isOwner && (\n          <div className=\"flex justify-end space-x-2 mt-2\">\n            {isEditing ? (\n              <>\n                <button\n                  onClick={handleSave}\n                  className=\"p-1.5 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors flex items-center gap-1 text-xs\"\n                >\n                  <Save size={12} />\n                  Save\n                </button>\n                <button\n                  onClick={onCancelEdit}\n                  className=\"p-1.5 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors flex items-center gap-1 text-xs\"\n                >\n                  <X size={12} />\n                  Cancel\n                </button>\n              </>\n            ) : (\n              <>\n                <button\n                  onClick={onEdit}\n                  className=\"p-1.5 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\"\n                >\n                  <Edit3 size={12} />\n                  Edit\n                </button>\n                <button\n                  onClick={onDelete}\n                  className=\"p-1.5 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100 flex items-center gap-1 text-xs\"\n                >\n                  <Trash2 size={12} />\n                  Delete\n                </button>\n              </>\n            )}\n          </div>\n        )}\n        \n        {isEditing && (\n          <p className=\"text-xs text-gray-500 mt-2 flex items-center gap-1\">\n            <span>Ctrl+Enter to save, Escape to cancel</span>\n          </p>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default Notes;\n\n\n\n\n\n\n\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,EAAEC,UAAU,QAAQ,cAAc;;AAE5H;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,QAAQ,GAAG;EACfC,QAAQ,EAAEA,CAAA,KAAMC,OAAO,CAACC,OAAO,CAAC,CAC9B;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,gBAAgB;IACvBC,OAAO,EAAE,gIAAgI;IACzIC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;IAChCC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE;EACV,CAAC,EACD;IACEN,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,4GAA4G;IACrHC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;IAC9BC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EACFC,UAAU,EAAGC,IAAI,IAAKV,OAAO,CAACC,OAAO,CAAC;IAAE,GAAGS,IAAI;IAAER,EAAE,EAAES,IAAI,CAACC,GAAG,CAAC;EAAE,CAAC,CAAC;EAClEC,UAAU,EAAEA,CAACX,EAAE,EAAEQ,IAAI,KAAKV,OAAO,CAACC,OAAO,CAAC;IAAE,GAAGS,IAAI;IAAER;EAAG,CAAC,CAAC;EAC1DY,UAAU,EAAGZ,EAAE,IAAKF,OAAO,CAACC,OAAO,CAAC,IAAI;AAC1C,CAAC;AAED,MAAMc,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAK,CAAC,GAAGtC,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEuC;EAAS,CAAC,GAAGtC,QAAQ,CAAC,CAAC;EAC/B,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC;IACrC0B,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXE,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF7B,SAAS,CAAC,MAAM;IACdyD,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtC,QAAQ,CAACC,QAAQ,CAAC,CAAC;MAC1CqB,QAAQ,CAACgB,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;MAC1Bd,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5CjB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;MACdE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMb,UAAU,GAAG,MAAO8B,CAAC,IAAK;IAC9BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACP,OAAO,CAAC7B,OAAO,CAACqC,IAAI,CAAC,CAAC,EAAE;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAG;QACnB,GAAGT,OAAO;QACVzB,MAAM,EAAE,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,KAAI,WAAW;QACjCC,WAAW,EAAE,CAAA3B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,KAAI,WAAW;QACtCE,OAAO,EAAE,CAAA5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEf,EAAE,KAAI,CAAC;QACtB4C,UAAU,EAAE,IAAInC,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIrC,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC;MACrC,CAAC;MACD,MAAMX,QAAQ,GAAG,MAAMtC,QAAQ,CAACW,UAAU,CAACiC,YAAY,CAAC;MACxDtB,QAAQ,CAAC6B,IAAI,IAAI,CAACb,QAAQ,EAAE,GAAGa,IAAI,CAAC,CAAC,CAAC,CAAC;MACvCf,UAAU,CAAC;QACT/B,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE,EAAE;QACXE,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE;MACT,CAAC,CAAC;MACFiB,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5Ca,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,MAAMrC,UAAU,GAAG,MAAAA,CAAOsC,MAAM,EAAE/C,OAAO,KAAK;IAC5C,IAAI;MACF,MAAMgD,WAAW,GAAG;QAClB,GAAGjC,KAAK,CAACkC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACpD,EAAE,KAAKiD,MAAM,CAAC;QACnC/C,OAAO;QACP4C,UAAU,EAAE,IAAIrC,IAAI,CAAC,CAAC,CAACoC,WAAW,CAAC;MACrC,CAAC;MACD,MAAMX,QAAQ,GAAG,MAAMtC,QAAQ,CAACe,UAAU,CAACsC,MAAM,EAAEC,WAAW,CAAC;MAC/DhC,QAAQ,CAAC6B,IAAI,IAAIA,IAAI,CAACM,GAAG,CAAC7C,IAAI,IAC5BA,IAAI,CAACR,EAAE,KAAKiD,MAAM,GAAGf,QAAQ,GAAG1B,IAClC,CAAC,CAAC;MACFgB,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5Ca,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,MAAMpC,UAAU,GAAG,MAAOqC,MAAM,IAAK;IACnC,IAAI,CAACK,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC,EAAE;IAEnE,IAAI;MACF,MAAM3D,QAAQ,CAACgB,UAAU,CAACqC,MAAM,CAAC;MACjC/B,QAAQ,CAAC6B,IAAI,IAAIA,IAAI,CAACS,MAAM,CAAChD,IAAI,IAAIA,IAAI,CAACR,EAAE,KAAKiD,MAAM,CAAC,CAAC;IAC3D,CAAC,CAAC,OAAOd,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5Ca,KAAK,CAAC,qBAAqB,CAAC;IAC9B;EACF,CAAC;EAED,MAAMS,cAAc,GAAIpB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACqB,GAAG,KAAK,OAAO,IAAI,CAACrB,CAAC,CAACsB,QAAQ,EAAE;MACpCtB,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB/B,UAAU,CAAC8B,CAAC,CAAC;IACf;EACF,CAAC;;EAED;EACA,MAAMuB,aAAa,GAAG3C,KAAK,CAACuC,MAAM,CAAChD,IAAI,IAAI;IACzC,MAAMqD,aAAa,GAAGrD,IAAI,CAACP,KAAK,CAAC6D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,UAAU,CAACqC,WAAW,CAAC,CAAC,CAAC,IAC5DtD,IAAI,CAACN,OAAO,CAAC4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtC,UAAU,CAACqC,WAAW,CAAC,CAAC,CAAC;IAClF,MAAME,aAAa,GAAGnC,QAAQ,KAAK,KAAK,IAClBA,QAAQ,KAAK,MAAM,IAAIrB,IAAI,CAACF,MAAM,MAAKS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC,IAClDZ,QAAQ,KAAK,QAAQ,IAAIrB,IAAI,CAACF,MAAM,MAAKS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE0B,IAAI,CAAC;IAC1E,OAAOoB,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,UAAU,GAAG,CACjB,iDAAiD,EACjD,2CAA2C,EAC3C,2CAA2C,EAC3C,8CAA8C,EAC9C,iDAAiD,EACjD,iDAAiD,EACjD,wCAAwC,EACxC,iDAAiD,CAClD;EAED,IAAI9C,OAAO,EAAE;IACX,oBACE1B,OAAA;MAAKyE,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC1E,OAAA;QAAKyE,SAAS,EAAC;MAA6E;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnG9E,OAAA;QAAGyE,SAAS,EAAC,kEAAkE;QAAAC,QAAA,gBAC7E1E,OAAA,CAACF,UAAU;UAAC2E,SAAS,EAAC,eAAe;UAACM,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,6BAEpD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEV;EAEA,oBACE9E,OAAA;IAAKyE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB1E,OAAA;MAAKyE,SAAS,EAAC,iIAAiI;MAAAC,QAAA,gBAE9I1E,OAAA;QAAKyE,SAAS,EAAC,0DAA0D;QAAAC,QAAA,EAAC;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAClF9E,OAAA;QAAKyE,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAEnF9E,OAAA;QAAKyE,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3C1E,OAAA;UAAKyE,SAAS,EAAC,8GAA8G;UAAAC,QAAA,eAC3H1E,OAAA,CAACJ,OAAO;YAAC6E,SAAS,EAAC,YAAY;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACN9E,OAAA;UAAIyE,SAAS,EAAC,0DAA0D;UAAAC,QAAA,GAAC,mBAEvE,eAAA1E,OAAA,CAACb,KAAK;YAACsF,SAAS,EAAC,4BAA4B;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEN9E,OAAA;QAAMgF,QAAQ,EAAElE,UAAW;QAAC2D,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC/C1E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA;YACEiF,KAAK,EAAE3C,OAAQ;YACf4C,QAAQ,EAAGtC,CAAC,IAAKL,UAAU,CAACK,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;YAC5CG,UAAU,EAAEpB,cAAe;YAC3BqB,WAAW,EAAC,sEAA4D;YACxEC,IAAI,EAAE,CAAE;YACRb,SAAS,EAAC;UAAwO;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnP,CAAC,eACF9E,OAAA;YAAKyE,SAAS,EAAC,yCAAyC;YAAAC,QAAA,eACtD1E,OAAA,CAACF,UAAU;cAACiF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChD1E,OAAA;YACEuF,IAAI,EAAC,QAAQ;YACbC,QAAQ,EAAE,CAAClD,OAAO,CAACQ,IAAI,CAAC,CAAE;YAC1B2B,SAAS,EAAC,gUAAgU;YAAAC,QAAA,gBAE1U1E,OAAA,CAACd,IAAI;cAAC6F,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA;YAAGyE,SAAS,EAAC,+CAA+C;YAAAC,QAAA,eAC1D1E,OAAA;cAAA0E,QAAA,EAAM;YAAiD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGN9E,OAAA;MAAKyE,SAAS,EAAC,4GAA4G;MAAAC,QAAA,gBACzH1E,OAAA;QAAKyE,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtC1E,OAAA;UAAKyE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB1E,OAAA,CAACN,MAAM;YAAC+E,SAAS,EAAC,kEAAkE;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjG9E,OAAA;YACEuF,IAAI,EAAC,MAAM;YACXF,WAAW,EAAC,iBAAiB;YAC7BJ,KAAK,EAAEjD,UAAW;YAClBkD,QAAQ,EAAGtC,CAAC,IAAKX,aAAa,CAACW,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;YAC/CR,SAAS,EAAC;UAA2I;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN9E,OAAA;UAAKyE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC1E,OAAA,CAACL,MAAM;YAAC8E,SAAS,EAAC,eAAe;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9C9E,OAAA;YACEiF,KAAK,EAAE7C,QAAS;YAChB8C,QAAQ,EAAGtC,CAAC,IAAKP,WAAW,CAACO,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;YAC7CR,SAAS,EAAC,0GAA0G;YAAAC,QAAA,gBAEpH1E,OAAA;cAAQiF,KAAK,EAAC,KAAK;cAAAP,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9E,OAAA;cAAQiF,KAAK,EAAC,MAAM;cAAAP,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC9E,OAAA;cAAQiF,KAAK,EAAC,QAAQ;cAAAP,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN9E,OAAA;QAAKyE,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE1E,OAAA,CAACF,UAAU;UAACiF,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACvBX,aAAa,CAACsB,MAAM,EAAC,aACxB;MAAA;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9E,OAAA;MAAKyE,SAAS,EAAC,wGAAwG;MAAAC,QAAA,gBACrH1E,OAAA;QAAKyE,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3C1E,OAAA;UAAKyE,SAAS,EAAC,6GAA6G;UAAAC,QAAA,eAC1H1E,OAAA,CAACb,KAAK;YAACsF,SAAS,EAAC,YAAY;YAACM,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACN9E,OAAA;UAAIyE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,EAELX,aAAa,CAACsB,MAAM,KAAK,CAAC,gBACzBzF,OAAA;QAAKyE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC1E,OAAA;UAAKyE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC9E,OAAA;UAAIyE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EACjD1C,UAAU,GAAG,gBAAgB,GAAG;QAAc;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACL9E,OAAA;UAAGyE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAC9B1C,UAAU,GAAG,6BAA6B,GAAG;QAAgC;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC,eACJ9E,OAAA;UAAKyE,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD1E,OAAA;YAAMyE,SAAS,EAAC,gBAAgB;YAACiB,KAAK,EAAE;cAACC,cAAc,EAAE;YAAI,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzE9E,OAAA;YAAMyE,SAAS,EAAC,gBAAgB;YAACiB,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3E9E,OAAA;YAAMyE,SAAS,EAAC,gBAAgB;YAACiB,KAAK,EAAE;cAACC,cAAc,EAAE;YAAM,CAAE;YAAAjB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAEN9E,OAAA;QAAKyE,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjFP,aAAa,CAACP,GAAG,CAAC,CAAC7C,IAAI,EAAE6E,KAAK,kBAC7B5F,OAAA,CAAC6F,QAAQ;UAEP9E,IAAI,EAAEA,IAAK;UACX+E,UAAU,EAAEtB,UAAU,CAACoB,KAAK,GAAGpB,UAAU,CAACiB,MAAM,CAAE;UAClDM,OAAO,EAAEhF,IAAI,CAACmC,OAAO,KAAK5B,IAAI,CAACf,EAAG;UAClCyF,SAAS,EAAElE,WAAW,KAAKf,IAAI,CAACR,EAAG;UACnC0F,MAAM,EAAEA,CAAA,KAAMlE,cAAc,CAAChB,IAAI,CAACR,EAAE,CAAE;UACtC2F,YAAY,EAAEA,CAAA,KAAMnE,cAAc,CAAC,IAAI,CAAE;UACzCoE,MAAM,EAAG1F,OAAO,IAAKS,UAAU,CAACH,IAAI,CAACR,EAAE,EAAEE,OAAO,CAAE;UAClD2F,QAAQ,EAAEA,CAAA,KAAMjF,UAAU,CAACJ,IAAI,CAACR,EAAE;QAAE,GAR/BQ,IAAI,CAACR,EAAE;UAAAoE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASb,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAzD,EAAA,CArQMD,KAAK;EAAA,QACQpC,OAAO,EACHC,QAAQ;AAAA;AAAAoH,EAAA,GAFzBjF,KAAK;AAsQX,MAAMyE,QAAQ,GAAGA,CAAC;EAAE9E,IAAI;EAAE+E,UAAU;EAAEC,OAAO;EAAEC,SAAS;EAAEC,MAAM;EAAEC,YAAY;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAE,GAAA;EACrG,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1H,QAAQ,CAACiC,IAAI,CAACN,OAAO,CAAC;EAE5D,MAAMgG,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIF,WAAW,CAACzD,IAAI,CAAC,CAAC,EAAE;MACtBqD,MAAM,CAACI,WAAW,CAACzD,IAAI,CAAC,CAAC,CAAC;IAC5B;EACF,CAAC;EAED,MAAMkB,cAAc,GAAIpB,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACqB,GAAG,KAAK,OAAO,IAAIrB,CAAC,CAAC8D,OAAO,EAAE;MAClC9D,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB4D,UAAU,CAAC,CAAC;IACd;IACA,IAAI7D,CAAC,CAACqB,GAAG,KAAK,QAAQ,EAAE;MACtBiC,YAAY,CAAC,CAAC;MACdM,cAAc,CAACzF,IAAI,CAACN,OAAO,CAAC;IAC9B;EACF,CAAC;EAED,oBACET,OAAA;IAAKyE,SAAS,EAAE,qBAAqBqB,UAAU,oKAAqK;IAAApB,QAAA,gBAElN1E,OAAA;MAAKyE,SAAS,EAAC,qFAAqF;MAAAC,QAAA,EACjGqB,OAAO,GAAG,IAAI,GAAG;IAAI;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC,eAGN9E,OAAA;MAAKyE,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBsB,SAAS,gBACRhG,OAAA;QACEiF,KAAK,EAAEsB,WAAY;QACnBrB,QAAQ,EAAGtC,CAAC,IAAK4D,cAAc,CAAC5D,CAAC,CAACuC,MAAM,CAACF,KAAK,CAAE;QAChDG,UAAU,EAAEpB,cAAe;QAC3BS,SAAS,EAAC,6IAA6I;QACvJkC,SAAS;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAEF9E,OAAA;QAAGyE,SAAS,EAAC,uEAAuE;QAAAC,QAAA,EACjF3D,IAAI,CAACN;MAAO;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IACJ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGN9E,OAAA;MAAKyE,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpD1E,OAAA;QAAKyE,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3E1E,OAAA;UAAKyE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC1E,OAAA,CAACH,IAAI;YAACkF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAClB9E,OAAA;YAAMyE,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAE3D,IAAI,CAACkC;UAAW;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN9E,OAAA;UAAKyE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC1E,OAAA,CAACR,QAAQ;YAACuF,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtB9E,OAAA;YAAA0E,QAAA,EAAO,IAAI1D,IAAI,CAACD,IAAI,CAACoC,UAAU,CAAC,CAACyD,kBAAkB,CAAC;UAAC;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL/D,IAAI,CAACsC,UAAU,KAAKtC,IAAI,CAACoC,UAAU,iBAClCnD,OAAA;QAAKyE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,gBACjE1E,OAAA,CAACZ,KAAK;UAAC2F,IAAI,EAAE;QAAG;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnB9E,OAAA;UAAA0E,QAAA,GAAM,SAAO,EAAC,IAAI1D,IAAI,CAACD,IAAI,CAACsC,UAAU,CAAC,CAACuD,kBAAkB,CAAC,CAAC;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACN,EAGAiB,OAAO,iBACN/F,OAAA;QAAKyE,SAAS,EAAC,iCAAiC;QAAAC,QAAA,EAC7CsB,SAAS,gBACRhG,OAAA,CAAAE,SAAA;UAAAwE,QAAA,gBACE1E,OAAA;YACE6G,OAAO,EAAEJ,UAAW;YACpBhC,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAEzH1E,OAAA,CAACV,IAAI;cAACyF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA;YACE6G,OAAO,EAAEX,YAAa;YACtBzB,SAAS,EAAC,6GAA6G;YAAAC,QAAA,gBAEvH1E,OAAA,CAACT,CAAC;cAACwF,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEjB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CAAC,gBAEH9E,OAAA,CAAAE,SAAA;UAAAwE,QAAA,gBACE1E,OAAA;YACE6G,OAAO,EAAEZ,MAAO;YAChBxB,SAAS,EAAC,+IAA+I;YAAAC,QAAA,gBAEzJ1E,OAAA,CAACZ,KAAK;cAAC2F,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,QAErB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT9E,OAAA;YACE6G,OAAO,EAAET,QAAS;YAClB3B,SAAS,EAAC,6IAA6I;YAAAC,QAAA,gBAEvJ1E,OAAA,CAACX,MAAM;cAAC0F,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAEAkB,SAAS,iBACRhG,OAAA;QAAGyE,SAAS,EAAC,oDAAoD;QAAAC,QAAA,eAC/D1E,OAAA;UAAA0E,QAAA,EAAM;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CACJ;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACwB,GAAA,CAjHIT,QAAQ;AAAAiB,GAAA,GAARjB,QAAQ;AAmHd,eAAezE,KAAK;AAAC,IAAAiF,EAAA,EAAAS,GAAA;AAAAC,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
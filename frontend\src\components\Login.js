import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { Heart, Mail, Lock, Eye, EyeOff, Sparkles } from 'lucide-react';

const Login = () => {
  const { login } = useAuth();
  const { darkMode } = useTheme();
  const [credentials, setCredentials] = useState({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      await login(credentials.email, credentials.password);
    } catch (err) {
      setError(err.message || 'Invalid credentials. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = () => {
    setShowForgotPassword(true);
  };

  const FloatingElement = ({ children, delay = 0, duration = 6 }) => (
    <div
      className="absolute opacity-20 pointer-events-none select-none"
      style={{
        left: `${Math.random() * 90 + 5}%`,
        top: `${Math.random() * 90 + 5}%`,
        animation: `float ${duration}s ease-in-out infinite ${delay}s`,
      }}
    >
      {children}
    </div>
  );

  return (
    <>
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          33% { transform: translateY(-20px) rotate(5deg); }
          66% { transform: translateY(10px) rotate(-3deg); }
        }
        
        @keyframes pulse-glow {
          0%, 100% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); }
          50% { box-shadow: 0 0 40px rgba(212, 175, 55, 0.6), 0 0 60px rgba(212, 175, 55, 0.3); }
        }
        
        @keyframes gradient-shift {
          0% { background-position: 0% 50%; }
          50% { background-position: 100% 50%; }
          100% { background-position: 0% 50%; }
        }
        
        @keyframes heartbeat {
          0%, 100% { transform: scale(1); }
          25% { transform: scale(1.1); }
          50% { transform: scale(1.05); }
          75% { transform: scale(1.15); }
        }
        
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        
        .love-gradient {
          background: linear-gradient(
            135deg,
            #f7f3f0 0%,
            #e8d5d5 25%,
            #d4c4b0 50%,
            #c4b59f 75%,
            #b8a9c9 100%
          );
          background-size: 400% 400%;
          animation: gradient-shift 8s ease infinite;
        }
        
        .glass-card {
          background: rgba(247, 243, 240, 0.85);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(212, 175, 55, 0.2);
          box-shadow: 
            0 25px 50px -12px rgba(44, 24, 16, 0.15),
            0 0 0 1px rgba(212, 175, 55, 0.1) inset;
        }
        
        .input-glow:focus {
          animation: pulse-glow 2s ease-in-out infinite;
        }
        
        .btn-love {
          background: linear-gradient(135deg, #d4af37, #e6c547, #d4af37);
          background-size: 200% 200%;
          position: relative;
          overflow: hidden;
          color: #2c1810;
        }
        
        .btn-love::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.4),
            transparent
          );
          transition: left 0.5s;
        }
        
        .btn-love:hover::before {
          left: 100%;
        }
        
        .btn-love:hover {
          background-position: 100% 0;
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);
        }
        
        .floating-particles {
          position: absolute;
          width: 100%;
          height: 100%;
          overflow: hidden;
          pointer-events: none;
        }
        
        .particle {
          position: absolute;
          width: 4px;
          height: 4px;
          background: rgba(212, 175, 55, 0.6);
          border-radius: 50%;
          animation: float 8s linear infinite;
        }
        
        .interactive-bg {
          background: radial-gradient(
            circle at ${mousePosition.x}% ${mousePosition.y}%,
            rgba(212, 175, 55, 0.2) 0%,
            transparent 50%
          );
          transition: background 0.3s ease;
        }
      `}</style>
      
      <div className={`min-h-screen flex items-center justify-center transition-all duration-500 ${
        darkMode 
          ? 'bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900' 
          : 'bg-gradient-to-br from-pink-100 via-purple-100 to-indigo-100'
      } relative overflow-hidden`}>
        {/* Animated background elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className={`absolute top-20 left-20 w-72 h-72 ${
            darkMode ? 'bg-purple-500/20' : 'bg-pink-300/30'
          } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob`}></div>
          <div className={`absolute top-40 right-20 w-72 h-72 ${
            darkMode ? 'bg-blue-500/20' : 'bg-purple-300/30'
          } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000`}></div>
          <div className={`absolute -bottom-8 left-40 w-72 h-72 ${
            darkMode ? 'bg-pink-500/20' : 'bg-indigo-300/30'
          } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000`}></div>
        </div>

        {/* Floating hearts */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-float opacity-20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${5 + Math.random() * 3}s`
              }}
            >
              <span className={`text-2xl ${darkMode ? 'text-purple-300' : 'text-pink-400'}`}>
                {['💕', '💖', '💗', '💝', '💘'][Math.floor(Math.random() * 5)]}
              </span>
            </div>
          ))}
        </div>

        {/* Main Login Card - Compact */}
        <div className={`relative z-10 w-full max-w-md mx-auto p-8 ${
          darkMode 
            ? 'bg-gray-800/80 border-gray-700' 
            : 'bg-white/80 border-white/20'
        } backdrop-blur-lg rounded-3xl shadow-2xl border`}>
          <div className="glass-card rounded-2xl p-6 transform hover:scale-[1.02] transition-all duration-500">
            
            {/* Header Section - Compact */}
            <div className="text-center mb-6">
              <div 
                className="text-5xl mb-3 inline-block"
                style={{ animation: 'heartbeat 2s ease-in-out infinite' }}
              >
                💝
              </div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-amber-600 via-amber-700 to-amber-800 bg-clip-text text-transparent mb-1">
                {showForgotPassword ? 'Recovery' : 'Love Awaits'}
              </h1>
              <p className="text-amber-800 text-base font-medium">
                {showForgotPassword ? 'Your special credentials' : 'Enter your heart\'s sanctuary'}
              </p>
            </div>

            {showForgotPassword ? (
              // Forgot Password View - Compact
              <div className="space-y-4 animate-in slide-in-from-right duration-500">
                <div className="bg-gradient-to-r from-amber-50 to-amber-100 border border-amber-300 rounded-xl p-4 shadow-lg">
                  <div className="flex items-center mb-2">
                    <Heart className="text-amber-600 mr-2" size={16} />
                    <p className="font-bold text-amber-900 text-sm">Your Love Credentials:</p>
                  </div>
                  <div className="space-y-2 text-amber-800 text-sm">
                    <p className="flex items-center">
                      <Mail size={14} className="mr-2 text-amber-600" />
                      <span className="font-mono bg-white px-2 py-1 rounded text-xs"><EMAIL></span>
                    </p>
                    <p className="flex items-center">
                      <Lock size={14} className="mr-2 text-amber-600" />
                      <span className="font-mono bg-white px-2 py-1 rounded text-xs">mazzalin</span>
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowForgotPassword(false)}
                  className="w-full btn-love py-3 rounded-xl font-bold text-base transition-all duration-300 hover:shadow-xl"
                >
                  ← Return to Love
                </button>
              </div>
            ) : (
              // Login Form - Compact
              <form onSubmit={handleSubmit} className="space-y-4">
                {error && (
                  <div className="bg-gradient-to-r from-red-100 to-pink-100 border border-red-300 text-red-700 px-4 py-3 rounded-xl shadow-lg animate-in slide-in-from-top duration-300">
                    <div className="flex items-center">
                      <Heart className="text-red-500 mr-2 flex-shrink-0" size={16} />
                      <span className="font-medium text-sm">{error}</span>
                    </div>
                  </div>
                )}
                
                {/* Email Input - Compact */}
                <div className="space-y-1">
                  <label className="block text-xs font-bold text-amber-800 mb-1 flex items-center">
                    <Mail size={14} className="mr-1 text-amber-600" />
                    Email Address
                  </label>
                  <div className="relative">
                    <input
                      type="email"
                      required
                      className="w-full px-4 py-3 bg-white/70 border border-amber-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-200 focus:border-amber-500 input-glow transition-all duration-300 text-amber-900 placeholder-amber-600 text-sm"
                      value={credentials.email}
                      onChange={(e) => setCredentials({...credentials, email: e.target.value})}
                      placeholder="Enter your heart's email..."
                    />
                  </div>
                </div>
                
                {/* Password Input - Compact */}
                <div className="space-y-1">
                  <label className="block text-xs font-bold text-amber-800 mb-1 flex items-center">
                    <Lock size={14} className="mr-1 text-amber-600" />
                    Password
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      required
                      className="w-full px-4 py-3 pr-12 bg-white/70 border border-amber-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-200 focus:border-amber-500 input-glow transition-all duration-300 text-amber-900 placeholder-amber-600 text-sm"
                      value={credentials.password}
                      onChange={(e) => setCredentials({...credentials, password: e.target.value})}
                      placeholder="Your secret love code..."
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-amber-600 hover:text-amber-700 transition-colors duration-200 p-1"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                    </button>
                  </div>
                </div>
                
                {/* Forgot Password Link - Compact */}
                <div className="text-center">
                  <button
                    type="button"
                    onClick={handleForgotPassword}
                    className="text-amber-700 hover:text-amber-600 font-medium transition-colors duration-200 hover:underline text-sm"
                  >
                    Forgot your love password? 💭
                  </button>
                </div>
                
                {/* Submit Button - Compact */}
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full btn-love py-3 rounded-xl font-bold text-base transition-all duration-300 disabled:opacity-70 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                >
                  {loading ? (
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-amber-800 mr-2"></div>
                      <span>Unlocking hearts...</span>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center">
                      <Heart className="mr-2 animate-pulse" size={18} />
                      Enter Love Sanctuary
                      <Sparkles className="ml-2 animate-pulse" size={18} />
                    </div>
                  )}
                </button>
              </form>
            )}
            
            {/* Demo Credentials - Compact */}
            {!showForgotPassword && (
              <div className="mt-4 text-center">
                <div className="bg-gradient-to-r from-amber-100 to-amber-200 border border-amber-300 rounded-xl p-3 shadow-lg">
                  <p className="font-bold text-amber-900 mb-1 flex items-center justify-center text-sm">
                    <Sparkles size={14} className="mr-1 text-amber-600" />
                    Demo Love Portal
                    <Sparkles size={14} className="ml-1 text-amber-600" />
                  </p>
                  <p className="text-xs text-amber-800 font-mono bg-white px-2 py-1 rounded-lg inline-block">
                    <EMAIL> • mazzalin
                  </p>
                </div>
              </div>
            )}
            
          </div>
          
          {/* Bottom decorative text - Compact */}
          <div className="text-center mt-4">
            <p className="text-amber-800 font-medium text-base drop-shadow-lg">
              💕 Where hearts connect digitally 💕
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Login;





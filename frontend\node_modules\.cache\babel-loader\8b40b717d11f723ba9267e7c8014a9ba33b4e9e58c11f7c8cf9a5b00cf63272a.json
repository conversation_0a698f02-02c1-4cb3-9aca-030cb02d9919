{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from 'react';\nexport const useKeyboardShortcuts = shortcuts => {\n  _s();\n  useEffect(() => {\n    const handleKeyPress = event => {\n      // Don't trigger shortcuts when typing in inputs\n      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {\n        return;\n      }\n      const key = event.key.toLowerCase();\n      if (shortcuts[key]) {\n        event.preventDefault();\n        shortcuts[key]();\n      }\n    };\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [shortcuts]);\n};\n_s(useKeyboardShortcuts, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");", "map": {"version": 3, "names": ["useEffect", "useKeyboardShortcuts", "shortcuts", "_s", "handleKeyPress", "event", "target", "tagName", "key", "toLowerCase", "preventDefault", "window", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/hooks/useKeyboardShortcuts.js"], "sourcesContent": ["import { useEffect } from 'react';\n\nexport const useKeyboardShortcuts = (shortcuts) => {\n  useEffect(() => {\n    const handleKeyPress = (event) => {\n      // Don't trigger shortcuts when typing in inputs\n      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {\n        return;\n      }\n\n      const key = event.key.toLowerCase();\n      if (shortcuts[key]) {\n        event.preventDefault();\n        shortcuts[key]();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [shortcuts]);\n};\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,OAAO;AAEjC,OAAO,MAAMC,oBAAoB,GAAIC,SAAS,IAAK;EAAAC,EAAA;EACjDH,SAAS,CAAC,MAAM;IACd,MAAMI,cAAc,GAAIC,KAAK,IAAK;MAChC;MACA,IAAIA,KAAK,CAACC,MAAM,CAACC,OAAO,KAAK,OAAO,IAAIF,KAAK,CAACC,MAAM,CAACC,OAAO,KAAK,UAAU,EAAE;QAC3E;MACF;MAEA,MAAMC,GAAG,GAAGH,KAAK,CAACG,GAAG,CAACC,WAAW,CAAC,CAAC;MACnC,IAAIP,SAAS,CAACM,GAAG,CAAC,EAAE;QAClBH,KAAK,CAACK,cAAc,CAAC,CAAC;QACtBR,SAAS,CAACM,GAAG,CAAC,CAAC,CAAC;MAClB;IACF,CAAC;IAEDG,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAER,cAAc,CAAC;IAClD,OAAO,MAAMO,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAET,cAAc,CAAC;EACpE,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;AACjB,CAAC;AAACC,EAAA,CAlBWF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
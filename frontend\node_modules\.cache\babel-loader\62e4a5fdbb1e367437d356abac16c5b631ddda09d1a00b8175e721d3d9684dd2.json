{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Settings, Menu, X } from 'lucide-react';\nimport Home from './Home';\nimport Chat from './Chat';\nimport Photos from './Photos';\nimport Notes from './Notes';\nimport SettingsPanel from './SettingsPanel';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('home');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const {\n    user\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts({\n    '0': () => setActiveTab('home'),\n    '1': () => setActiveTab('chat'),\n    '2': () => setActiveTab('photos'),\n    '3': () => setActiveTab('notes'),\n    'escape': () => {\n      setShowSettings(false);\n      setShowMobileMenu(false);\n    },\n    's': () => setShowSettings(true)\n  });\n  const tabs = [{\n    id: 'home',\n    name: 'Home',\n    icon: '🏠',\n    shortcut: '0',\n    gradient: 'from-indigo-500 to-purple-500',\n    description: 'Your sanctuary'\n  }, {\n    id: 'chat',\n    name: 'Our Chats',\n    icon: '💬',\n    shortcut: '1',\n    gradient: 'from-pink-500 to-rose-500',\n    description: 'Sweet conversations'\n  }, {\n    id: 'photos',\n    name: 'Memories',\n    icon: '📸',\n    shortcut: '2',\n    gradient: 'from-purple-500 to-indigo-500',\n    description: 'Beautiful moments'\n  }, {\n    id: 'notes',\n    name: 'Love Notes',\n    icon: '💕',\n    shortcut: '3',\n    gradient: 'from-red-500 to-pink-500',\n    description: 'Heartfelt messages'\n  }];\n  const getTabTitle = () => {\n    switch (activeTab) {\n      case 'home':\n        return 'Welcome Home';\n      case 'chat':\n        return 'Our Sweet Conversations';\n      case 'photos':\n        return 'Our Beautiful Memories';\n      case 'notes':\n        return 'Love Notes & Thoughts';\n      default:\n        return 'Our Love Story';\n    }\n  };\n  const getTabIcon = () => {\n    switch (activeTab) {\n      case 'home':\n        return '🏠';\n      case 'chat':\n        return '💬';\n      case 'photos':\n        return '📸';\n      case 'notes':\n        return '💕';\n      default:\n        return '💖';\n    }\n  };\n  const getTabGradient = () => {\n    const tab = tabs.find(t => t.id === activeTab);\n    return (tab === null || tab === void 0 ? void 0 : tab.gradient) || 'from-pink-500 to-rose-500';\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen transition-colors duration-300 ${darkMode ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900' : 'bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50'} relative overflow-hidden`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute top-10 left-10 w-32 h-32 ${darkMode ? 'bg-purple-500/20' : 'bg-pink-200'} rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute top-32 right-20 w-40 h-40 ${darkMode ? 'bg-blue-500/20' : 'bg-purple-200'} rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute bottom-32 left-1/4 w-28 h-28 ${darkMode ? 'bg-pink-500/20' : 'bg-rose-200'} rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: `relative z-10 backdrop-blur-sm ${darkMode ? 'bg-gray-900/80 border-gray-700/50' : 'bg-white/80 border-pink-200/50'} shadow-lg border-b transition-colors duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDC96\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                children: \"Our Love Story\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: \"Together forever \\u221E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right hidden sm:block\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: `font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`,\n                children: \"Welcome back,\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent\",\n                children: [user === null || user === void 0 ? void 0 : user.name, \" \\uD83D\\uDC95\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSettings(true),\n              className: `p-3 ${darkMode ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'} rounded-full border transition-all duration-200 hover:shadow-md focus:outline-none \n                         focus:ring-2 focus:ring-pink-300 group`,\n              title: \"Settings (Press S)\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 20,\n                className: \"group-hover:rotate-90 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowMobileMenu(!showMobileMenu),\n              className: `sm:hidden p-3 ${darkMode ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'} rounded-full border transition-all duration-200 hover:shadow-md`,\n              children: showMobileMenu ? /*#__PURE__*/_jsxDEV(X, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: `relative z-10 ${darkMode ? 'bg-gray-800/60 border-gray-700/50' : 'bg-white/60 border-pink-100/50'} backdrop-blur-sm border-b transition-colors duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`,\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setActiveTab(tab.id);\n              setShowMobileMenu(false);\n            },\n            className: `flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${activeTab === tab.id ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105` : `${darkMode ? 'text-gray-300 hover:text-white hover:bg-gray-700/70' : 'text-gray-600 hover:text-gray-900 hover:bg-white/70'} hover:shadow-md`}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl group-hover:scale-110 transition-transform duration-200\",\n              children: tab.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold\",\n                children: tab.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs ${activeTab === tab.id ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: [tab.description, \" \\u2022 Press \", tab.shortcut]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this)]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 sm:px-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${darkMode ? 'bg-gray-800/70 border-gray-700/50' : 'bg-white/70 border-white/50'} backdrop-blur-sm rounded-3xl shadow-2xl border p-8 \n                         transition-all duration-500 hover:shadow-3xl min-h-[600px]`,\n          children: [activeTab !== 'home' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 bg-gradient-to-r ${getTabGradient()} rounded-full flex items-center justify-center`,\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white text-lg\",\n                children: getTabIcon()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n              children: getTabTitle()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"transition-all duration-300\",\n            children: [activeTab === 'home' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), activeTab === 'chat' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), activeTab === 'photos' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: /*#__PURE__*/_jsxDEV(Photos, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 17\n            }, this), activeTab === 'notes' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: /*#__PURE__*/_jsxDEV(Notes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsPanel, {\n      isOpen: showSettings,\n      onClose: () => setShowSettings(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"dUPNzhDgoqQKSNqhyLx24T1RKlE=\", false, function () {\n  return [useAuth, useTheme, useKeyboardShortcuts];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useTheme", "Settings", "<PERSON><PERSON>", "X", "Home", "Cha<PERSON>", "Photos", "Notes", "SettingsPanel", "useKeyboardShortcuts", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "activeTab", "setActiveTab", "showSettings", "setShowSettings", "showMobileMenu", "setShowMobileMenu", "user", "darkMode", "0", "1", "2", "3", "escape", "s", "tabs", "id", "name", "icon", "shortcut", "gradient", "description", "getTabTitle", "getTabIcon", "getTabGradient", "tab", "find", "t", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "size", "map", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Settings, Menu, X } from 'lucide-react';\nimport Home from './Home';\nimport Chat from './Chat';\nimport Photos from './Photos';\nimport Notes from './Notes';\nimport SettingsPanel from './SettingsPanel';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';\n\nconst Dashboard = () => {\n  const [activeTab, setActiveTab] = useState('home');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const { user } = useAuth();\n  const { darkMode } = useTheme();\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts({\n    '0': () => setActiveTab('home'),\n    '1': () => setActiveTab('chat'),\n    '2': () => setActiveTab('photos'),\n    '3': () => setActiveTab('notes'),\n    'escape': () => {\n      setShowSettings(false);\n      setShowMobileMenu(false);\n    },\n    's': () => setShowSettings(true)\n  });\n\n  const tabs = [\n    { \n      id: 'home', \n      name: 'Home', \n      icon: '🏠', \n      shortcut: '0',\n      gradient: 'from-indigo-500 to-purple-500',\n      description: 'Your sanctuary'\n    },\n    { \n      id: 'chat', \n      name: 'Our Chats', \n      icon: '💬', \n      shortcut: '1',\n      gradient: 'from-pink-500 to-rose-500',\n      description: 'Sweet conversations'\n    },\n    { \n      id: 'photos', \n      name: 'Memories', \n      icon: '📸', \n      shortcut: '2',\n      gradient: 'from-purple-500 to-indigo-500',\n      description: 'Beautiful moments'\n    },\n    { \n      id: 'notes', \n      name: 'Love Notes', \n      icon: '💕', \n      shortcut: '3',\n      gradient: 'from-red-500 to-pink-500',\n      description: 'Heartfelt messages'\n    }\n  ];\n\n  const getTabTitle = () => {\n    switch(activeTab) {\n      case 'home': return 'Welcome Home';\n      case 'chat': return 'Our Sweet Conversations';\n      case 'photos': return 'Our Beautiful Memories';\n      case 'notes': return 'Love Notes & Thoughts';\n      default: return 'Our Love Story';\n    }\n  };\n\n  const getTabIcon = () => {\n    switch(activeTab) {\n      case 'home': return '🏠';\n      case 'chat': return '💬';\n      case 'photos': return '📸';\n      case 'notes': return '💕';\n      default: return '💖';\n    }\n  };\n\n  const getTabGradient = () => {\n    const tab = tabs.find(t => t.id === activeTab);\n    return tab?.gradient || 'from-pink-500 to-rose-500';\n  };\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${\n      darkMode \n        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900' \n        : 'bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50'\n    } relative overflow-hidden`}>\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className={`absolute top-10 left-10 w-32 h-32 ${\n          darkMode ? 'bg-purple-500/20' : 'bg-pink-200'\n        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse`}></div>\n        <div className={`absolute top-32 right-20 w-40 h-40 ${\n          darkMode ? 'bg-blue-500/20' : 'bg-purple-200'\n        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000`}></div>\n        <div className={`absolute bottom-32 left-1/4 w-28 h-28 ${\n          darkMode ? 'bg-pink-500/20' : 'bg-rose-200'\n        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000`}></div>\n      </div>\n\n      {/* Header */}\n      <header className={`relative z-10 backdrop-blur-sm ${\n        darkMode \n          ? 'bg-gray-900/80 border-gray-700/50' \n          : 'bg-white/80 border-pink-200/50'\n      } shadow-lg border-b transition-colors duration-300`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\">\n                <span className=\"text-2xl\">💖</span>\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n                  Our Love Story\n                </h1>\n                <p className={`text-sm font-medium ${\n                  darkMode ? 'text-gray-400' : 'text-gray-500'\n                }`}>Together forever ∞</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-right hidden sm:block\">\n                <p className={`font-medium ${\n                  darkMode ? 'text-gray-300' : 'text-gray-700'\n                }`}>Welcome back,</p>\n                <p className=\"text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent\">\n                  {user?.name} 💕\n                </p>\n              </div>\n              \n              {/* Settings Button */}\n              <button\n                onClick={() => setShowSettings(true)}\n                className={`p-3 ${\n                  darkMode \n                    ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' \n                    : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'\n                } rounded-full border transition-all duration-200 hover:shadow-md focus:outline-none \n                         focus:ring-2 focus:ring-pink-300 group`}\n                title=\"Settings (Press S)\"\n              >\n                <Settings size={20} className=\"group-hover:rotate-90 transition-transform duration-300\" />\n              </button>\n              \n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setShowMobileMenu(!showMobileMenu)}\n                className={`sm:hidden p-3 ${\n                  darkMode \n                    ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' \n                    : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'\n                } rounded-full border transition-all duration-200 hover:shadow-md`}\n              >\n                {showMobileMenu ? <X size={20} /> : <Menu size={20} />}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className={`relative z-10 ${\n        darkMode \n          ? 'bg-gray-800/60 border-gray-700/50' \n          : 'bg-white/60 border-pink-100/50'\n      } backdrop-blur-sm border-b transition-colors duration-300`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className={`flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`}>\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => {\n                  setActiveTab(tab.id);\n                  setShowMobileMenu(false);\n                }}\n                className={`flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${\n                  activeTab === tab.id\n                    ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105`\n                    : `${\n                        darkMode \n                          ? 'text-gray-300 hover:text-white hover:bg-gray-700/70' \n                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/70'\n                      } hover:shadow-md`\n                }`}\n              >\n                <span className=\"text-2xl group-hover:scale-110 transition-transform duration-200\">\n                  {tab.icon}\n                </span>\n                <div className=\"text-left\">\n                  <div className=\"font-bold\">{tab.name}</div>\n                  <div className={`text-xs ${\n                    activeTab === tab.id \n                      ? 'text-white/80' \n                      : darkMode ? 'text-gray-400' : 'text-gray-500'\n                  }`}>\n                    {tab.description} • Press {tab.shortcut}\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Content */}\n      <main className=\"relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\">\n        <div className=\"px-4 sm:px-0\">\n          <div className={`${\n            darkMode \n              ? 'bg-gray-800/70 border-gray-700/50' \n              : 'bg-white/70 border-white/50'\n          } backdrop-blur-sm rounded-3xl shadow-2xl border p-8 \n                         transition-all duration-500 hover:shadow-3xl min-h-[600px]`}>\n            \n            {/* Tab Header - Only show for non-home tabs */}\n            {activeTab !== 'home' && (\n              <div className=\"flex items-center space-x-3 mb-6\">\n                <div className={`w-10 h-10 bg-gradient-to-r ${getTabGradient()} rounded-full flex items-center justify-center`}>\n                  <span className=\"text-white text-lg\">{getTabIcon()}</span>\n                </div>\n                <h2 className={`text-2xl font-bold ${\n                  darkMode ? 'text-white' : 'text-gray-800'\n                }`}>{getTabTitle()}</h2>\n              </div>\n            )}\n\n            <div className=\"transition-all duration-300\">\n              {activeTab === 'home' && (\n                <div className=\"animate-fade-in\">\n                  <Home />\n                </div>\n              )}\n              {activeTab === 'chat' && (\n                <div className=\"animate-fade-in\">\n                  <Chat />\n                </div>\n              )}\n              {activeTab === 'photos' && (\n                <div className=\"animate-fade-in\">\n                  <Photos />\n                </div>\n              )}\n              {activeTab === 'notes' && (\n                <div className=\"animate-fade-in\">\n                  <Notes />\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Settings Panel */}\n      <SettingsPanel \n        isOpen={showSettings} \n        onClose={() => setShowSettings(false)} \n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,QAAQ,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,oBAAoB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGjB,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAEsB;EAAK,CAAC,GAAGrB,OAAO,CAAC,CAAC;EAC1B,MAAM;IAAEsB;EAAS,CAAC,GAAGrB,QAAQ,CAAC,CAAC;;EAE/B;EACAS,oBAAoB,CAAC;IACnB,GAAG,EAAEa,CAAA,KAAMP,YAAY,CAAC,MAAM,CAAC;IAC/B,GAAG,EAAEQ,CAAA,KAAMR,YAAY,CAAC,MAAM,CAAC;IAC/B,GAAG,EAAES,CAAA,KAAMT,YAAY,CAAC,QAAQ,CAAC;IACjC,GAAG,EAAEU,CAAA,KAAMV,YAAY,CAAC,OAAO,CAAC;IAChC,QAAQ,EAAEW,CAAA,KAAM;MACdT,eAAe,CAAC,KAAK,CAAC;MACtBE,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC;IACD,GAAG,EAAEQ,CAAA,KAAMV,eAAe,CAAC,IAAI;EACjC,CAAC,CAAC;EAEF,MAAMW,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,+BAA+B;IACzCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,2BAA2B;IACrCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,+BAA+B;IACzCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB,QAAOrB,SAAS;MACd,KAAK,MAAM;QAAE,OAAO,cAAc;MAClC,KAAK,MAAM;QAAE,OAAO,yBAAyB;MAC7C,KAAK,QAAQ;QAAE,OAAO,wBAAwB;MAC9C,KAAK,OAAO;QAAE,OAAO,uBAAuB;MAC5C;QAAS,OAAO,gBAAgB;IAClC;EACF,CAAC;EAED,MAAMsB,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAOtB,SAAS;MACd,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,MAAM;QAAE,OAAO,IAAI;MACxB,KAAK,QAAQ;QAAE,OAAO,IAAI;MAC1B,KAAK,OAAO;QAAE,OAAO,IAAI;MACzB;QAAS,OAAO,IAAI;IACtB;EACF,CAAC;EAED,MAAMuB,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,GAAG,GAAGV,IAAI,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACX,EAAE,KAAKf,SAAS,CAAC;IAC9C,OAAO,CAAAwB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEL,QAAQ,KAAI,2BAA2B;EACrD,CAAC;EAED,oBACEtB,OAAA;IAAK8B,SAAS,EAAE,+CACdpB,QAAQ,GACJ,4DAA4D,GAC5D,yDAAyD,2BACnC;IAAAqB,QAAA,gBAE1B/B,OAAA;MAAK8B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnE/B,OAAA;QAAK8B,SAAS,EAAE,qCACdpB,QAAQ,GAAG,kBAAkB,GAAG,aAAa;MAC4B;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClFnC,OAAA;QAAK8B,SAAS,EAAE,sCACdpB,QAAQ,GAAG,gBAAgB,GAAG,eAAe;MACuC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7FnC,OAAA;QAAK8B,SAAS,EAAE,yCACdpB,QAAQ,GAAG,gBAAgB,GAAG,aAAa;MACyC;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CAAC,eAGNnC,OAAA;MAAQ8B,SAAS,EAAE,kCACjBpB,QAAQ,GACJ,mCAAmC,GACnC,gCAAgC,oDACe;MAAAqB,QAAA,eACnD/B,OAAA;QAAK8B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD/B,OAAA;UAAK8B,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrD/B,OAAA;YAAK8B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C/B,OAAA;cAAK8B,SAAS,EAAC,4HAA4H;cAAAC,QAAA,eACzI/B,OAAA;gBAAM8B,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNnC,OAAA;cAAA+B,QAAA,gBACE/B,OAAA;gBAAI8B,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAAC;cAE9G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLnC,OAAA;gBAAG8B,SAAS,EAAE,uBACZpB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC3C;gBAAAqB,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENnC,OAAA;YAAK8B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C/B,OAAA;cAAK8B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzC/B,OAAA;gBAAG8B,SAAS,EAAE,eACZpB,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC3C;gBAAAqB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrBnC,OAAA;gBAAG8B,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,GACxGtB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,EAAC,eACd;cAAA;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNnC,OAAA;cACEoC,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAAC,IAAI,CAAE;cACrCwB,SAAS,EAAE,OACTpB,QAAQ,GACJ,6GAA6G,GAC7G,uGAAuG;AAC7H,gEACiE;cACjD2B,KAAK,EAAC,oBAAoB;cAAAN,QAAA,eAE1B/B,OAAA,CAACV,QAAQ;gBAACgD,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAyD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eAGTnC,OAAA;cACEoC,OAAO,EAAEA,CAAA,KAAM5B,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAClDuB,SAAS,EAAE,iBACTpB,QAAQ,GACJ,6GAA6G,GAC7G,uGAAuG,kEAC1C;cAAAqB,QAAA,EAElExB,cAAc,gBAAGP,OAAA,CAACR,CAAC;gBAAC8C,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGnC,OAAA,CAACT,IAAI;gBAAC+C,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTnC,OAAA;MAAK8B,SAAS,EAAE,iBACdpB,QAAQ,GACJ,mCAAmC,GACnC,gCAAgC,2DACsB;MAAAqB,QAAA,eAC1D/B,OAAA;QAAK8B,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD/B,OAAA;UAAK8B,SAAS,EAAE,uCAAuCvB,cAAc,GAAG,OAAO,GAAG,gBAAgB,EAAG;UAAAwB,QAAA,EAClGd,IAAI,CAACsB,GAAG,CAAEZ,GAAG,iBACZ3B,OAAA;YAEEoC,OAAO,EAAEA,CAAA,KAAM;cACbhC,YAAY,CAACuB,GAAG,CAACT,EAAE,CAAC;cACpBV,iBAAiB,CAAC,KAAK,CAAC;YAC1B,CAAE;YACFsB,SAAS,EAAE,qHACT3B,SAAS,KAAKwB,GAAG,CAACT,EAAE,GAChB,oBAAoBS,GAAG,CAACL,QAAQ,2CAA2C,GAC3E,GACEZ,QAAQ,GACJ,qDAAqD,GACrD,qDAAqD,kBACzC,EACrB;YAAAqB,QAAA,gBAEH/B,OAAA;cAAM8B,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC/EJ,GAAG,CAACP;YAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACPnC,OAAA;cAAK8B,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB/B,OAAA;gBAAK8B,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAEJ,GAAG,CAACR;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3CnC,OAAA;gBAAK8B,SAAS,EAAE,WACd3B,SAAS,KAAKwB,GAAG,CAACT,EAAE,GAChB,eAAe,GACfR,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC/C;gBAAAqB,QAAA,GACAJ,GAAG,CAACJ,WAAW,EAAC,gBAAS,EAACI,GAAG,CAACN,QAAQ;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA3BDR,GAAG,CAACT,EAAE;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4BL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnC,OAAA;MAAM8B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACpE/B,OAAA;QAAK8B,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3B/B,OAAA;UAAK8B,SAAS,EAAE,GACdpB,QAAQ,GACJ,mCAAmC,GACnC,6BAA6B;AAC7C,oFACqF;UAAAqB,QAAA,GAGxE5B,SAAS,KAAK,MAAM,iBACnBH,OAAA;YAAK8B,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/C/B,OAAA;cAAK8B,SAAS,EAAE,8BAA8BJ,cAAc,CAAC,CAAC,gDAAiD;cAAAK,QAAA,eAC7G/B,OAAA;gBAAM8B,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAEN,UAAU,CAAC;cAAC;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNnC,OAAA;cAAI8B,SAAS,EAAE,sBACbpB,QAAQ,GAAG,YAAY,GAAG,eAAe,EACxC;cAAAqB,QAAA,EAAEP,WAAW,CAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CACN,eAEDnC,OAAA;YAAK8B,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzC5B,SAAS,KAAK,MAAM,iBACnBH,OAAA;cAAK8B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/B,OAAA,CAACP,IAAI;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EACAhC,SAAS,KAAK,MAAM,iBACnBH,OAAA;cAAK8B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/B,OAAA,CAACN,IAAI;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EACAhC,SAAS,KAAK,QAAQ,iBACrBH,OAAA;cAAK8B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/B,OAAA,CAACL,MAAM;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACN,EACAhC,SAAS,KAAK,OAAO,iBACpBH,OAAA;cAAK8B,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC9B/B,OAAA,CAACJ,KAAK;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnC,OAAA,CAACH,aAAa;MACZ2C,MAAM,EAAEnC,YAAa;MACrBoC,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,KAAK;IAAE;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACjC,EAAA,CApQID,SAAS;EAAA,QAIIb,OAAO,EACHC,QAAQ,EAG7BS,oBAAoB;AAAA;AAAA4C,EAAA,GARhBzC,SAAS;AAsQf,eAAeA,SAAS;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
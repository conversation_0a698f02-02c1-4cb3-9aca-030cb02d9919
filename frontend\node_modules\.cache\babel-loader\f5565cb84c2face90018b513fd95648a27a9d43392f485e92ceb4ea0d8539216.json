{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport Dashboard from './components/Dashboard';\nimport LoadingTransition from './components/LoadingTransition';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  const [showTransition, setShowTransition] = useState(true);\n  const [transitionComplete, setTransitionComplete] = useState(false);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 12\n    }, this);\n  }\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 12\n    }, this);\n  }\n  if (showTransition && !transitionComplete) {\n    return /*#__PURE__*/_jsxDEV(LoadingTransition, {\n      user: user,\n      onComplete: () => {\n        setTransitionComplete(true);\n        setShowTransition(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"AaH91z6jLuM8aHyablpoi0LqsZY=\", false, function () {\n  return [useAuth];\n});\n_c = ProtectedRoute;\nconst AppRoutes = () => {\n  _s2();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(Routes, {\n    children: [/*#__PURE__*/_jsxDEV(Route, {\n      path: \"/login\",\n      element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n        to: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 47\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Route, {\n      path: \"/\",\n      element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n        children: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n};\n_s2(AppRoutes, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c2 = AppRoutes;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"AppRoutes\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "useState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "<PERSON><PERSON>", "Dashboard", "LoadingTransition", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "loading", "showTransition", "setShowTransition", "transitionComplete", "setTransitionComplete", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onComplete", "_c", "AppRoutes", "_s2", "path", "element", "_c2", "App", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/App.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { AuthProvider, useAuth } from './context/AuthContext';\nimport Login from './components/Login';\nimport Dashboard from './components/Dashboard';\nimport LoadingTransition from './components/LoadingTransition';\n\nconst ProtectedRoute = ({ children }) => {\n  const { user, loading } = useAuth();\n  const [showTransition, setShowTransition] = useState(true);\n  const [transitionComplete, setTransitionComplete] = useState(false);\n  \n  if (loading) {\n    return <div className=\"min-h-screen flex items-center justify-center\">Loading...</div>;\n  }\n  \n  if (!user) {\n    return <Navigate to=\"/login\" />;\n  }\n\n  if (showTransition && !transitionComplete) {\n    return (\n      <LoadingTransition \n        user={user}\n        onComplete={() => {\n          setTransitionComplete(true);\n          setShowTransition(false);\n        }}\n      />\n    );\n  }\n  \n  return children;\n};\n\nconst AppRoutes = () => {\n  const { user } = useAuth();\n  \n  return (\n    <Routes>\n      <Route \n        path=\"/login\" \n        element={user ? <Navigate to=\"/\" /> : <Login />} \n      />\n      <Route \n        path=\"/\" \n        element={\n          <ProtectedRoute>\n            <Dashboard />\n          </ProtectedRoute>\n        } \n      />\n    </Routes>\n  );\n};\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <AppRoutes />\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n\n\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,YAAY,EAAEC,OAAO,QAAQ,uBAAuB;AAC7D,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGV,OAAO,CAAC,CAAC;EACnC,MAAM,CAACW,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAEnE,IAAIiB,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKU,SAAS,EAAC,+CAA+C;MAAAR,QAAA,EAAC;IAAU;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACxF;EAEA,IAAI,CAACV,IAAI,EAAE;IACT,oBAAOJ,OAAA,CAACP,QAAQ;MAACsB,EAAE,EAAC;IAAQ;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACjC;EAEA,IAAIR,cAAc,IAAI,CAACE,kBAAkB,EAAE;IACzC,oBACER,OAAA,CAACF,iBAAiB;MAChBM,IAAI,EAAEA,IAAK;MACXY,UAAU,EAAEA,CAAA,KAAM;QAChBP,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IAAE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEN;EAEA,OAAOZ,QAAQ;AACjB,CAAC;AAACC,EAAA,CA1BIF,cAAc;EAAA,QACQN,OAAO;AAAA;AAAAsB,EAAA,GAD7BhB,cAAc;AA4BpB,MAAMiB,SAAS,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtB,MAAM;IAAEf;EAAK,CAAC,GAAGT,OAAO,CAAC,CAAC;EAE1B,oBACEK,OAAA,CAACT,MAAM;IAAAW,QAAA,gBACLF,OAAA,CAACR,KAAK;MACJ4B,IAAI,EAAC,QAAQ;MACbC,OAAO,EAAEjB,IAAI,gBAAGJ,OAAA,CAACP,QAAQ;QAACsB,EAAE,EAAC;MAAG;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGd,OAAA,CAACJ,KAAK;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eACFd,OAAA,CAACR,KAAK;MACJ4B,IAAI,EAAC,GAAG;MACRC,OAAO,eACLrB,OAAA,CAACC,cAAc;QAAAC,QAAA,eACbF,OAAA,CAACH,SAAS;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IACjB;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;AAACK,GAAA,CAnBID,SAAS;EAAA,QACIvB,OAAO;AAAA;AAAA2B,GAAA,GADpBJ,SAAS;AAqBf,SAASK,GAAGA,CAAA,EAAG;EACb,oBACEvB,OAAA,CAACN,YAAY;IAAAQ,QAAA,eACXF,OAAA,CAACV,MAAM;MAAAY,QAAA,eACLF,OAAA;QAAKU,SAAS,EAAC,KAAK;QAAAR,QAAA,eAClBF,OAAA,CAACkB,SAAS;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACU,GAAA,GAVQD,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAN,EAAA,EAAAK,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
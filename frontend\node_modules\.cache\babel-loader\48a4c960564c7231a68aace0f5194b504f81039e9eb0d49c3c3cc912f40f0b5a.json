{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\LoadingTransition.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Heart } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingTransition = ({\n  onComplete\n}) => {\n  _s();\n  const [progress, setProgress] = useState(0);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const slides = [{\n    emoji: '💕',\n    text: 'Loading our love story...'\n  }, {\n    emoji: '🌹',\n    text: 'Preparing beautiful moments...'\n  }, {\n    emoji: '✨',\n    text: 'Creating magical memories...'\n  }, {\n    emoji: '💖',\n    text: 'Almost ready for you...'\n  }];\n  useEffect(() => {\n    const slideInterval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % slides.length);\n    }, 1000);\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          setTimeout(onComplete, 200);\n          return 100;\n        }\n        return prev + 5;\n      });\n    }, 50);\n    return () => {\n      clearInterval(slideInterval);\n      clearInterval(progressInterval);\n    };\n  }, [onComplete, slides.length]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-pink-100 via-purple-50 to-indigo-100 overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0\",\n      children: [...Array(15)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute animate-float opacity-30\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`,\n          animationDelay: `${Math.random() * 3}s`,\n          animationDuration: `${4 + Math.random() * 2}s`\n        },\n        children: /*#__PURE__*/_jsxDEV(Heart, {\n          className: \"text-pink-400\",\n          size: 20 + Math.random() * 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 13\n        }, this)\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 text-center max-w-md mx-auto px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-8xl mb-6 animate-bounce\",\n          children: slides[currentSlide].emoji\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-800 mb-4 animate-pulse\",\n          children: slides[currentSlide].text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full bg-white/50 rounded-full h-3 mb-4 overflow-hidden shadow-inner\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"h-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 rounded-full transition-all duration-300 ease-out relative overflow-hidden\",\n          style: {\n            width: `${progress}%`\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 animate-pulse\",\n        children: [progress, \"% Complete\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 37,\n    columnNumber: 5\n  }, this);\n};\n_s(LoadingTransition, \"7gR2FarJtKQAcge7SletqwUwrN4=\");\n_c = LoadingTransition;\nexport default LoadingTransition;\nvar _c;\n$RefreshReg$(_c, \"LoadingTransition\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Heart", "jsxDEV", "_jsxDEV", "LoadingTransition", "onComplete", "_s", "progress", "setProgress", "currentSlide", "setCurrentSlide", "slides", "emoji", "text", "slideInterval", "setInterval", "prev", "length", "progressInterval", "setTimeout", "clearInterval", "className", "children", "Array", "map", "_", "i", "style", "left", "Math", "random", "top", "animationDelay", "animationDuration", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/LoadingTransition.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Heart } from 'lucide-react';\n\nconst LoadingTransition = ({ onComplete }) => {\n  const [progress, setProgress] = useState(0);\n  const [currentSlide, setCurrentSlide] = useState(0);\n  \n  const slides = [\n    { emoji: '💕', text: 'Loading our love story...' },\n    { emoji: '🌹', text: 'Preparing beautiful moments...' },\n    { emoji: '✨', text: 'Creating magical memories...' },\n    { emoji: '💖', text: 'Almost ready for you...' }\n  ];\n\n  useEffect(() => {\n    const slideInterval = setInterval(() => {\n      setCurrentSlide(prev => (prev + 1) % slides.length);\n    }, 1000);\n\n    const progressInterval = setInterval(() => {\n      setProgress(prev => {\n        if (prev >= 100) {\n          setTimeout(onComplete, 200);\n          return 100;\n        }\n        return prev + 5;\n      });\n    }, 50);\n\n    return () => {\n      clearInterval(slideInterval);\n      clearInterval(progressInterval);\n    };\n  }, [onComplete, slides.length]);\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-pink-100 via-purple-50 to-indigo-100 overflow-hidden\">\n      {/* Floating hearts animation */}\n      <div className=\"absolute inset-0\">\n        {[...Array(15)].map((_, i) => (\n          <div\n            key={i}\n            className=\"absolute animate-float opacity-30\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n              animationDelay: `${Math.random() * 3}s`,\n              animationDuration: `${4 + Math.random() * 2}s`\n            }}\n          >\n            <Heart className=\"text-pink-400\" size={20 + Math.random() * 20} />\n          </div>\n        ))}\n      </div>\n\n      <div className=\"relative z-10 text-center max-w-md mx-auto px-8\">\n        {/* Main loading content */}\n        <div className=\"mb-8\">\n          <div className=\"text-8xl mb-6 animate-bounce\">\n            {slides[currentSlide].emoji}\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 mb-4 animate-pulse\">\n            {slides[currentSlide].text}\n          </h2>\n        </div>\n\n        {/* Progress bar */}\n        <div className=\"w-full bg-white/50 rounded-full h-3 mb-4 overflow-hidden shadow-inner\">\n          <div \n            className=\"h-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 rounded-full transition-all duration-300 ease-out relative overflow-hidden\"\n            style={{ width: `${progress}%` }}\n          >\n            <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shimmer\"></div>\n          </div>\n        </div>\n\n        <p className=\"text-sm text-gray-600 animate-pulse\">\n          {progress}% Complete\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default LoadingTransition;\n\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErC,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACU,YAAY,EAAEC,eAAe,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAMY,MAAM,GAAG,CACb;IAAEC,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE;EAA4B,CAAC,EAClD;IAAED,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAiC,CAAC,EACvD;IAAED,KAAK,EAAE,GAAG;IAAEC,IAAI,EAAE;EAA+B,CAAC,EACpD;IAAED,KAAK,EAAE,IAAI;IAAEC,IAAI,EAAE;EAA0B,CAAC,CACjD;EAEDb,SAAS,CAAC,MAAM;IACd,MAAMc,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtCL,eAAe,CAACM,IAAI,IAAI,CAACA,IAAI,GAAG,CAAC,IAAIL,MAAM,CAACM,MAAM,CAAC;IACrD,CAAC,EAAE,IAAI,CAAC;IAER,MAAMC,gBAAgB,GAAGH,WAAW,CAAC,MAAM;MACzCP,WAAW,CAACQ,IAAI,IAAI;QAClB,IAAIA,IAAI,IAAI,GAAG,EAAE;UACfG,UAAU,CAACd,UAAU,EAAE,GAAG,CAAC;UAC3B,OAAO,GAAG;QACZ;QACA,OAAOW,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IAEN,OAAO,MAAM;MACXI,aAAa,CAACN,aAAa,CAAC;MAC5BM,aAAa,CAACF,gBAAgB,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,CAACb,UAAU,EAAEM,MAAM,CAACM,MAAM,CAAC,CAAC;EAE/B,oBACEd,OAAA;IAAKkB,SAAS,EAAC,iIAAiI;IAAAC,QAAA,gBAE9InB,OAAA;MAAKkB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,EAC9B,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBvB,OAAA;QAEEkB,SAAS,EAAC,mCAAmC;QAC7CM,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC9BE,cAAc,EAAE,GAAGH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;UACvCG,iBAAiB,EAAE,GAAG,CAAC,GAAGJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;QAC7C,CAAE;QAAAR,QAAA,eAEFnB,OAAA,CAACF,KAAK;UAACoB,SAAS,EAAC,eAAe;UAACa,IAAI,EAAE,EAAE,GAAGL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAAG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC,GAT7DZ,CAAC;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUH,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENnC,OAAA;MAAKkB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAE9DnB,OAAA;QAAKkB,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBnB,OAAA;UAAKkB,SAAS,EAAC,8BAA8B;UAAAC,QAAA,EAC1CX,MAAM,CAACF,YAAY,CAAC,CAACG;QAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC,eACNnC,OAAA;UAAIkB,SAAS,EAAC,qDAAqD;UAAAC,QAAA,EAChEX,MAAM,CAACF,YAAY,CAAC,CAACI;QAAI;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNnC,OAAA;QAAKkB,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eACpFnB,OAAA;UACEkB,SAAS,EAAC,+IAA+I;UACzJM,KAAK,EAAE;YAAEY,KAAK,EAAE,GAAGhC,QAAQ;UAAI,CAAE;UAAAe,QAAA,eAEjCnB,OAAA;YAAKkB,SAAS,EAAC;UAAgG;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENnC,OAAA;QAAGkB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,GAC/Cf,QAAQ,EAAC,YACZ;MAAA;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChC,EAAA,CA/EIF,iBAAiB;AAAAoC,EAAA,GAAjBpC,iBAAiB;AAiFvB,eAAeA,iBAAiB;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
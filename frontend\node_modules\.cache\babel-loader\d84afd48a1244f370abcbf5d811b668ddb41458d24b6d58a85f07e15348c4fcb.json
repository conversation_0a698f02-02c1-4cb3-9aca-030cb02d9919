{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Settings, Menu, X } from 'lucide-react';\nimport Chat from './Chat';\nimport Photos from './Photos';\nimport Notes from './Notes';\nimport SettingsPanel from './SettingsPanel';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('chat');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    darkMode\n  } = useTheme();\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts({\n    '1': () => setActiveTab('chat'),\n    '2': () => setActiveTab('photos'),\n    '3': () => setActiveTab('notes'),\n    'escape': () => {\n      setShowSettings(false);\n      setShowMobileMenu(false);\n    },\n    's': () => setShowSettings(true)\n  });\n  const tabs = [{\n    id: 'chat',\n    name: 'Our Chats',\n    icon: '💬',\n    shortcut: '1',\n    gradient: 'from-pink-500 to-rose-500',\n    description: 'Sweet conversations'\n  }, {\n    id: 'photos',\n    name: 'Memories',\n    icon: '📸',\n    shortcut: '2',\n    gradient: 'from-purple-500 to-indigo-500',\n    description: 'Beautiful moments'\n  }, {\n    id: 'notes',\n    name: 'Love Notes',\n    icon: '💕',\n    shortcut: '3',\n    gradient: 'from-red-500 to-pink-500',\n    description: 'Heartfelt messages'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen transition-colors duration-300 ${darkMode ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900' : 'bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50'} relative overflow-hidden`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute top-10 left-10 w-32 h-32 ${darkMode ? 'bg-purple-500/20' : 'bg-pink-200'} rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute top-32 right-20 w-40 h-40 ${darkMode ? 'bg-blue-500/20' : 'bg-purple-200'} rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `absolute bottom-32 left-1/4 w-28 h-28 ${darkMode ? 'bg-pink-500/20' : 'bg-rose-200'} rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: `relative z-10 backdrop-blur-sm ${darkMode ? 'bg-gray-900/80 border-gray-700/50' : 'bg-white/80 border-pink-200/50'} shadow-lg border-b transition-colors duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDC96\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                children: \"Our Love Story\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: `text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: \"Together forever \\u221E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right hidden sm:block\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: `font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`,\n                children: \"Welcome back,\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent\",\n                children: [user === null || user === void 0 ? void 0 : user.name, \" \\uD83D\\uDC95\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSettings(true),\n              className: `p-3 ${darkMode ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'} rounded-full border transition-all duration-200 hover:shadow-md focus:outline-none \n                         focus:ring-2 focus:ring-pink-300 group`,\n              title: \"Settings (Press S)\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 20,\n                className: \"group-hover:rotate-90 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowMobileMenu(!showMobileMenu),\n              className: `sm:hidden p-3 ${darkMode ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'} rounded-full border transition-all duration-200 hover:shadow-md`,\n              children: showMobileMenu ? /*#__PURE__*/_jsxDEV(X, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: `relative z-10 ${darkMode ? 'bg-gray-800/60 border-gray-700/50' : 'bg-white/60 border-pink-100/50'} backdrop-blur-sm border-b transition-colors duration-300`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`,\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setActiveTab(tab.id);\n              setShowMobileMenu(false);\n            },\n            className: `flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${activeTab === tab.id ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105` : `${darkMode ? 'text-gray-300 hover:text-white hover:bg-gray-700/70' : 'text-gray-600 hover:text-gray-900 hover:bg-white/70'} hover:shadow-md`}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl group-hover:scale-110 transition-transform duration-200\",\n              children: tab.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold\",\n                children: tab.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs ${activeTab === tab.id ? 'text-white/80' : darkMode ? 'text-gray-400' : 'text-gray-500'}`,\n                children: [tab.description, \" \\u2022 Press \", tab.shortcut]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 sm:px-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${darkMode ? 'bg-gray-800/70 border-gray-700/50' : 'bg-white/70 border-white/50'} backdrop-blur-sm rounded-3xl shadow-2xl border p-8 \n                         transition-all duration-500 hover:shadow-3xl min-h-[600px]`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"transition-all duration-300\",\n            children: [activeTab === 'chat' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-lg\",\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n                  children: \"Our Sweet Conversations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 17\n            }, this), activeTab === 'photos' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-lg\",\n                    children: \"\\uD83D\\uDCF8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 209,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n                  children: \"Our Beautiful Memories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Photos, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 215,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this), activeTab === 'notes' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-lg\",\n                    children: \"\\uD83D\\uDC95\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: `text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-800'}`,\n                  children: \"Love Notes & Thoughts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Notes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsPanel, {\n      isOpen: showSettings,\n      onClose: () => setShowSettings(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"mcWmBX2dwt4WRVyESL5BrbecP+I=\", false, function () {\n  return [useAuth, useTheme, useKeyboardShortcuts];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "useTheme", "Settings", "<PERSON><PERSON>", "X", "Cha<PERSON>", "Photos", "Notes", "SettingsPanel", "useKeyboardShortcuts", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "activeTab", "setActiveTab", "showSettings", "setShowSettings", "showMobileMenu", "setShowMobileMenu", "user", "logout", "darkMode", "1", "2", "3", "escape", "s", "tabs", "id", "name", "icon", "shortcut", "gradient", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "size", "map", "tab", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { Settings, Menu, X } from 'lucide-react';\nimport Chat from './Chat';\nimport Photos from './Photos';\nimport Notes from './Notes';\nimport SettingsPanel from './SettingsPanel';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';\n\nconst Dashboard = () => {\n  const [activeTab, setActiveTab] = useState('chat');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const { user, logout } = useAuth();\n  const { darkMode } = useTheme();\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts({\n    '1': () => setActiveTab('chat'),\n    '2': () => setActiveTab('photos'),\n    '3': () => setActiveTab('notes'),\n    'escape': () => {\n      setShowSettings(false);\n      setShowMobileMenu(false);\n    },\n    's': () => setShowSettings(true)\n  });\n\n  const tabs = [\n    { \n      id: 'chat', \n      name: 'Our Chats', \n      icon: '💬', \n      shortcut: '1',\n      gradient: 'from-pink-500 to-rose-500',\n      description: 'Sweet conversations'\n    },\n    { \n      id: 'photos', \n      name: 'Memories', \n      icon: '📸', \n      shortcut: '2',\n      gradient: 'from-purple-500 to-indigo-500',\n      description: 'Beautiful moments'\n    },\n    { \n      id: 'notes', \n      name: 'Love Notes', \n      icon: '💕', \n      shortcut: '3',\n      gradient: 'from-red-500 to-pink-500',\n      description: 'Heartfelt messages'\n    }\n  ];\n\n  return (\n    <div className={`min-h-screen transition-colors duration-300 ${\n      darkMode \n        ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-purple-900' \n        : 'bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50'\n    } relative overflow-hidden`}>\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className={`absolute top-10 left-10 w-32 h-32 ${\n          darkMode ? 'bg-purple-500/20' : 'bg-pink-200'\n        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse`}></div>\n        <div className={`absolute top-32 right-20 w-40 h-40 ${\n          darkMode ? 'bg-blue-500/20' : 'bg-purple-200'\n        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000`}></div>\n        <div className={`absolute bottom-32 left-1/4 w-28 h-28 ${\n          darkMode ? 'bg-pink-500/20' : 'bg-rose-200'\n        } rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000`}></div>\n      </div>\n\n      {/* Header */}\n      <header className={`relative z-10 backdrop-blur-sm ${\n        darkMode \n          ? 'bg-gray-900/80 border-gray-700/50' \n          : 'bg-white/80 border-pink-200/50'\n      } shadow-lg border-b transition-colors duration-300`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\">\n                <span className=\"text-2xl\">💖</span>\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n                  Our Love Story\n                </h1>\n                <p className={`text-sm font-medium ${\n                  darkMode ? 'text-gray-400' : 'text-gray-500'\n                }`}>Together forever ∞</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-right hidden sm:block\">\n                <p className={`font-medium ${\n                  darkMode ? 'text-gray-300' : 'text-gray-700'\n                }`}>Welcome back,</p>\n                <p className=\"text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent\">\n                  {user?.name} 💕\n                </p>\n              </div>\n              \n              {/* Settings Button */}\n              <button\n                onClick={() => setShowSettings(true)}\n                className={`p-3 ${\n                  darkMode \n                    ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' \n                    : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'\n                } rounded-full border transition-all duration-200 hover:shadow-md focus:outline-none \n                         focus:ring-2 focus:ring-pink-300 group`}\n                title=\"Settings (Press S)\"\n              >\n                <Settings size={20} className=\"group-hover:rotate-90 transition-transform duration-300\" />\n              </button>\n              \n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setShowMobileMenu(!showMobileMenu)}\n                className={`sm:hidden p-3 ${\n                  darkMode \n                    ? 'text-gray-400 hover:text-gray-200 bg-gray-800/50 hover:bg-gray-700/80 border-gray-600 hover:border-gray-500' \n                    : 'text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 border-gray-200 hover:border-gray-300'\n                } rounded-full border transition-all duration-200 hover:shadow-md`}\n              >\n                {showMobileMenu ? <X size={20} /> : <Menu size={20} />}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className={`relative z-10 ${\n        darkMode \n          ? 'bg-gray-800/60 border-gray-700/50' \n          : 'bg-white/60 border-pink-100/50'\n      } backdrop-blur-sm border-b transition-colors duration-300`}>\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className={`flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`}>\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => {\n                  setActiveTab(tab.id);\n                  setShowMobileMenu(false);\n                }}\n                className={`flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${\n                  activeTab === tab.id\n                    ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105`\n                    : `${\n                        darkMode \n                          ? 'text-gray-300 hover:text-white hover:bg-gray-700/70' \n                          : 'text-gray-600 hover:text-gray-900 hover:bg-white/70'\n                      } hover:shadow-md`\n                }`}\n              >\n                <span className=\"text-2xl group-hover:scale-110 transition-transform duration-200\">\n                  {tab.icon}\n                </span>\n                <div className=\"text-left\">\n                  <div className=\"font-bold\">{tab.name}</div>\n                  <div className={`text-xs ${\n                    activeTab === tab.id \n                      ? 'text-white/80' \n                      : darkMode ? 'text-gray-400' : 'text-gray-500'\n                  }`}>\n                    {tab.description} • Press {tab.shortcut}\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Content */}\n      <main className=\"relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\">\n        <div className=\"px-4 sm:px-0\">\n          <div className={`${\n            darkMode \n              ? 'bg-gray-800/70 border-gray-700/50' \n              : 'bg-white/70 border-white/50'\n          } backdrop-blur-sm rounded-3xl shadow-2xl border p-8 \n                         transition-all duration-500 hover:shadow-3xl min-h-[600px]`}>\n            <div className=\"transition-all duration-300\">\n              {activeTab === 'chat' && (\n                <div className=\"animate-fade-in\">\n                  <div className=\"flex items-center space-x-3 mb-6\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-lg\">💬</span>\n                    </div>\n                    <h2 className={`text-2xl font-bold ${\n                      darkMode ? 'text-white' : 'text-gray-800'\n                    }`}>Our Sweet Conversations</h2>\n                  </div>\n                  <Chat />\n                </div>\n              )}\n              {activeTab === 'photos' && (\n                <div className=\"animate-fade-in\">\n                  <div className=\"flex items-center space-x-3 mb-6\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-lg\">📸</span>\n                    </div>\n                    <h2 className={`text-2xl font-bold ${\n                      darkMode ? 'text-white' : 'text-gray-800'\n                    }`}>Our Beautiful Memories</h2>\n                  </div>\n                  <Photos />\n                </div>\n              )}\n              {activeTab === 'notes' && (\n                <div className=\"animate-fade-in\">\n                  <div className=\"flex items-center space-x-3 mb-6\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-lg\">💕</span>\n                    </div>\n                    <h2 className={`text-2xl font-bold ${\n                      darkMode ? 'text-white' : 'text-gray-800'\n                    }`}>Love Notes & Thoughts</h2>\n                  </div>\n                  <Notes />\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Settings Panel */}\n      <SettingsPanel \n        isOpen={showSettings} \n        onClose={() => setShowSettings(false)} \n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,QAAQ,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,oBAAoB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAEqB,IAAI;IAAEC;EAAO,CAAC,GAAGrB,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEsB;EAAS,CAAC,GAAGrB,QAAQ,CAAC,CAAC;;EAE/B;EACAQ,oBAAoB,CAAC;IACnB,GAAG,EAAEc,CAAA,KAAMR,YAAY,CAAC,MAAM,CAAC;IAC/B,GAAG,EAAES,CAAA,KAAMT,YAAY,CAAC,QAAQ,CAAC;IACjC,GAAG,EAAEU,CAAA,KAAMV,YAAY,CAAC,OAAO,CAAC;IAChC,QAAQ,EAAEW,CAAA,KAAM;MACdT,eAAe,CAAC,KAAK,CAAC;MACtBE,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC;IACD,GAAG,EAAEQ,CAAA,KAAMV,eAAe,CAAC,IAAI;EACjC,CAAC,CAAC;EAEF,MAAMW,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,2BAA2B;IACrCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,+BAA+B;IACzCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEvB,OAAA;IAAKwB,SAAS,EAAE,+CACdb,QAAQ,GACJ,4DAA4D,GAC5D,yDAAyD,2BACnC;IAAAc,QAAA,gBAE1BzB,OAAA;MAAKwB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEzB,OAAA;QAAKwB,SAAS,EAAE,qCACdb,QAAQ,GAAG,kBAAkB,GAAG,aAAa;MAC4B;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClF7B,OAAA;QAAKwB,SAAS,EAAE,sCACdb,QAAQ,GAAG,gBAAgB,GAAG,eAAe;MACuC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7F7B,OAAA;QAAKwB,SAAS,EAAE,yCACdb,QAAQ,GAAG,gBAAgB,GAAG,aAAa;MACyC;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1F,CAAC,eAGN7B,OAAA;MAAQwB,SAAS,EAAE,kCACjBb,QAAQ,GACJ,mCAAmC,GACnC,gCAAgC,oDACe;MAAAc,QAAA,eACnDzB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDzB,OAAA;UAAKwB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDzB,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzB,OAAA;cAAKwB,SAAS,EAAC,4HAA4H;cAAAC,QAAA,eACzIzB,OAAA;gBAAMwB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN7B,OAAA;cAAAyB,QAAA,gBACEzB,OAAA;gBAAIwB,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAAC;cAE9G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAGwB,SAAS,EAAE,uBACZb,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC3C;gBAAAc,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7B,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzB,OAAA;cAAKwB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCzB,OAAA;gBAAGwB,SAAS,EAAE,eACZb,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC3C;gBAAAc,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrB7B,OAAA;gBAAGwB,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,GACxGhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,IAAI,EAAC,eACd;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN7B,OAAA;cACE8B,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAAC,IAAI,CAAE;cACrCkB,SAAS,EAAE,OACTb,QAAQ,GACJ,6GAA6G,GAC7G,uGAAuG;AAC7H,gEACiE;cACjDoB,KAAK,EAAC,oBAAoB;cAAAN,QAAA,eAE1BzB,OAAA,CAACT,QAAQ;gBAACyC,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAyD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eAGT7B,OAAA;cACE8B,OAAO,EAAEA,CAAA,KAAMtB,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAClDiB,SAAS,EAAE,iBACTb,QAAQ,GACJ,6GAA6G,GAC7G,uGAAuG,kEAC1C;cAAAc,QAAA,EAElElB,cAAc,gBAAGP,OAAA,CAACP,CAAC;gBAACuC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG7B,OAAA,CAACR,IAAI;gBAACwC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT7B,OAAA;MAAKwB,SAAS,EAAE,iBACdb,QAAQ,GACJ,mCAAmC,GACnC,gCAAgC,2DACsB;MAAAc,QAAA,eAC1DzB,OAAA;QAAKwB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDzB,OAAA;UAAKwB,SAAS,EAAE,uCAAuCjB,cAAc,GAAG,OAAO,GAAG,gBAAgB,EAAG;UAAAkB,QAAA,EAClGR,IAAI,CAACgB,GAAG,CAAEC,GAAG,iBACZlC,OAAA;YAEE8B,OAAO,EAAEA,CAAA,KAAM;cACb1B,YAAY,CAAC8B,GAAG,CAAChB,EAAE,CAAC;cACpBV,iBAAiB,CAAC,KAAK,CAAC;YAC1B,CAAE;YACFgB,SAAS,EAAE,qHACTrB,SAAS,KAAK+B,GAAG,CAAChB,EAAE,GAChB,oBAAoBgB,GAAG,CAACZ,QAAQ,2CAA2C,GAC3E,GACEX,QAAQ,GACJ,qDAAqD,GACrD,qDAAqD,kBACzC,EACrB;YAAAc,QAAA,gBAEHzB,OAAA;cAAMwB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC/ES,GAAG,CAACd;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACP7B,OAAA;cAAKwB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzB,OAAA;gBAAKwB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAES,GAAG,CAACf;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3C7B,OAAA;gBAAKwB,SAAS,EAAE,WACdrB,SAAS,KAAK+B,GAAG,CAAChB,EAAE,GAChB,eAAe,GACfP,QAAQ,GAAG,eAAe,GAAG,eAAe,EAC/C;gBAAAc,QAAA,GACAS,GAAG,CAACX,WAAW,EAAC,gBAAS,EAACW,GAAG,CAACb,QAAQ;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA3BDK,GAAG,CAAChB,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4BL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7B,OAAA;MAAMwB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACpEzB,OAAA;QAAKwB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BzB,OAAA;UAAKwB,SAAS,EAAE,GACdb,QAAQ,GACJ,mCAAmC,GACnC,6BAA6B;AAC7C,oFACqF;UAAAc,QAAA,eACzEzB,OAAA;YAAKwB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzCtB,SAAS,KAAK,MAAM,iBACnBH,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzB,OAAA;gBAAKwB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CzB,OAAA;kBAAKwB,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,eACjHzB,OAAA;oBAAMwB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN7B,OAAA;kBAAIwB,SAAS,EAAE,sBACbb,QAAQ,GAAG,YAAY,GAAG,eAAe,EACxC;kBAAAc,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACN7B,OAAA,CAACN,IAAI;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EACA1B,SAAS,KAAK,QAAQ,iBACrBH,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzB,OAAA;gBAAKwB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CzB,OAAA;kBAAKwB,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,eACrHzB,OAAA;oBAAMwB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN7B,OAAA;kBAAIwB,SAAS,EAAE,sBACbb,QAAQ,GAAG,YAAY,GAAG,eAAe,EACxC;kBAAAc,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACN7B,OAAA,CAACL,MAAM;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACN,EACA1B,SAAS,KAAK,OAAO,iBACpBH,OAAA;cAAKwB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BzB,OAAA;gBAAKwB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CzB,OAAA;kBAAKwB,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,eAChHzB,OAAA;oBAAMwB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN7B,OAAA;kBAAIwB,SAAS,EAAE,sBACbb,QAAQ,GAAG,YAAY,GAAG,eAAe,EACxC;kBAAAc,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC,eACN7B,OAAA,CAACJ,KAAK;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP7B,OAAA,CAACH,aAAa;MACZsC,MAAM,EAAE9B,YAAa;MACrB+B,OAAO,EAAEA,CAAA,KAAM9B,eAAe,CAAC,KAAK;IAAE;MAAAoB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC3B,EAAA,CAxOID,SAAS;EAAA,QAIYZ,OAAO,EACXC,QAAQ,EAG7BQ,oBAAoB;AAAA;AAAAuC,EAAA,GARhBpC,SAAS;AA0Of,eAAeA,SAAS;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Heart, Eye, EyeOff, Mail, Lock, Sparkles } from 'lucide-react';\nimport { useAuth } from '../context/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  const [credentials, setCredentials] = useState({\n    email: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [showForgotPassword, setShowForgotPassword] = useState(false);\n  const [mousePosition, setMousePosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const {\n    login\n  } = useAuth();\n  useEffect(() => {\n    const handleMouseMove = e => {\n      setMousePosition({\n        x: e.clientX / window.innerWidth * 100,\n        y: e.clientY / window.innerHeight * 100\n      });\n    };\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const result = await login(credentials);\n      if (!result.success) {\n        setError(result.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleForgotPassword = () => {\n    setShowForgotPassword(true);\n    setError('');\n  };\n  const FloatingElement = ({\n    children,\n    delay = 0,\n    duration = 6\n  }) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"absolute opacity-20 pointer-events-none select-none\",\n    style: {\n      left: `${Math.random() * 90 + 5}%`,\n      top: `${Math.random() * 90 + 5}%`,\n      animation: `float ${duration}s ease-in-out infinite ${delay}s`\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          33% { transform: translateY(-20px) rotate(5deg); }\n          66% { transform: translateY(10px) rotate(-3deg); }\n        }\n        \n        @keyframes pulse-glow {\n          0%, 100% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); }\n          50% { box-shadow: 0 0 40px rgba(212, 175, 55, 0.6), 0 0 60px rgba(212, 175, 55, 0.3); }\n        }\n        \n        @keyframes gradient-shift {\n          0% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n          100% { background-position: 0% 50%; }\n        }\n        \n        @keyframes heartbeat {\n          0%, 100% { transform: scale(1); }\n          25% { transform: scale(1.1); }\n          50% { transform: scale(1.05); }\n          75% { transform: scale(1.15); }\n        }\n        \n        @keyframes shimmer {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n        \n        .love-gradient {\n          background: linear-gradient(\n            135deg,\n            #f7f3f0 0%,\n            #e8d5d5 25%,\n            #d4c4b0 50%,\n            #c4b59f 75%,\n            #b8a9c9 100%\n          );\n          background-size: 400% 400%;\n          animation: gradient-shift 8s ease infinite;\n        }\n        \n        .glass-card {\n          background: rgba(247, 243, 240, 0.85);\n          backdrop-filter: blur(20px);\n          border: 1px solid rgba(212, 175, 55, 0.2);\n          box-shadow: \n            0 25px 50px -12px rgba(44, 24, 16, 0.15),\n            0 0 0 1px rgba(212, 175, 55, 0.1) inset;\n        }\n        \n        .input-glow:focus {\n          animation: pulse-glow 2s ease-in-out infinite;\n        }\n        \n        .btn-love {\n          background: linear-gradient(135deg, #d4af37, #e6c547, #d4af37);\n          background-size: 200% 200%;\n          position: relative;\n          overflow: hidden;\n          color: #2c1810;\n        }\n        \n        .btn-love::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(\n            90deg,\n            transparent,\n            rgba(255, 255, 255, 0.4),\n            transparent\n          );\n          transition: left 0.5s;\n        }\n        \n        .btn-love:hover::before {\n          left: 100%;\n        }\n        \n        .btn-love:hover {\n          background-position: 100% 0;\n          transform: translateY(-2px);\n          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);\n        }\n        \n        .floating-particles {\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          overflow: hidden;\n          pointer-events: none;\n        }\n        \n        .particle {\n          position: absolute;\n          width: 4px;\n          height: 4px;\n          background: rgba(212, 175, 55, 0.6);\n          border-radius: 50%;\n          animation: float 8s linear infinite;\n        }\n        \n        .interactive-bg {\n          background: radial-gradient(\n            circle at ${mousePosition.x}% ${mousePosition.y}%,\n            rgba(212, 175, 55, 0.2) 0%,\n            transparent 50%\n          );\n          transition: background 0.3s ease;\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-screen flex items-center justify-center love-gradient relative overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 interactive-bg\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-particles\",\n        children: [...Array(10)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"particle\",\n          style: {\n            left: `${Math.random() * 100}%`,\n            animationDelay: `${i * 0.5}s`,\n            animationDuration: `${5 + Math.random() * 3}s`\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {\n        delay: 0,\n        duration: 5,\n        children: /*#__PURE__*/_jsxDEV(Heart, {\n          size: 20,\n          className: \"text-amber-400\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {\n        delay: 1,\n        duration: 7,\n        children: /*#__PURE__*/_jsxDEV(Sparkles, {\n          size: 16,\n          className: \"text-amber-300\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {\n        delay: 2,\n        duration: 6,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xl\",\n          children: \"\\uD83D\\uDC96\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FloatingElement, {\n        delay: 3,\n        duration: 8,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-lg\",\n          children: \"\\u2728\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-sm w-full mx-4 relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"glass-card rounded-2xl p-6 transform hover:scale-[1.02] transition-all duration-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-5xl mb-3 inline-block\",\n              style: {\n                animation: 'heartbeat 2s ease-in-out infinite'\n              },\n              children: \"\\uD83D\\uDC9D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold bg-gradient-to-r from-amber-600 via-amber-700 to-amber-800 bg-clip-text text-transparent mb-1\",\n              children: showForgotPassword ? 'Recovery' : 'Love Awaits'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-amber-800 text-base font-medium\",\n              children: showForgotPassword ? 'Your special credentials' : 'Enter your heart\\'s sanctuary'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), showForgotPassword ?\n          /*#__PURE__*/\n          // Forgot Password View - Compact\n          _jsxDEV(\"div\", {\n            className: \"space-y-4 animate-in slide-in-from-right duration-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-amber-50 to-amber-100 border border-amber-300 rounded-xl p-4 shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(Heart, {\n                  className: \"text-amber-600 mr-2\",\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-bold text-amber-900 text-sm\",\n                  children: \"Your Love Credentials:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2 text-amber-800 text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Mail, {\n                    size: 14,\n                    className: \"mr-2 text-amber-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-mono bg-white px-2 py-1 rounded text-xs\",\n                    children: \"<EMAIL>\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(Lock, {\n                    size: 14,\n                    className: \"mr-2 text-amber-600\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"font-mono bg-white px-2 py-1 rounded text-xs\",\n                    children: \"mazzalin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowForgotPassword(false),\n              className: \"w-full btn-love py-3 rounded-xl font-bold text-base transition-all duration-300 hover:shadow-xl\",\n              children: \"\\u2190 Return to Love\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this) :\n          /*#__PURE__*/\n          // Login Form - Compact\n          _jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-red-100 to-pink-100 border border-red-300 text-red-700 px-4 py-3 rounded-xl shadow-lg animate-in slide-in-from-top duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Heart, {\n                  className: \"text-red-500 mr-2 flex-shrink-0\",\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-sm\",\n                  children: error\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-bold text-amber-800 mb-1 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Mail, {\n                  size: 14,\n                  className: \"mr-1 text-amber-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this), \"Email Address\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  required: true,\n                  className: \"w-full px-4 py-3 bg-white/70 border border-amber-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-200 focus:border-amber-500 input-glow transition-all duration-300 text-amber-900 placeholder-amber-600 text-sm\",\n                  value: credentials.email,\n                  onChange: e => setCredentials({\n                    ...credentials,\n                    email: e.target.value\n                  }),\n                  placeholder: \"Enter your heart's email...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-xs font-bold text-amber-800 mb-1 flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(Lock, {\n                  size: 14,\n                  className: \"mr-1 text-amber-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), \"Password\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPassword ? \"text\" : \"password\",\n                  required: true,\n                  className: \"w-full px-4 py-3 pr-12 bg-white/70 border border-amber-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-200 focus:border-amber-500 input-glow transition-all duration-300 text-amber-900 placeholder-amber-600 text-sm\",\n                  value: credentials.password,\n                  onChange: e => setCredentials({\n                    ...credentials,\n                    password: e.target.value\n                  }),\n                  placeholder: \"Your secret love code...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-amber-600 hover:text-amber-700 transition-colors duration-200 p-1\",\n                  onClick: () => setShowPassword(!showPassword),\n                  children: showPassword ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 39\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 62\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: handleForgotPassword,\n                className: \"text-amber-700 hover:text-amber-600 font-medium transition-colors duration-200 hover:underline text-sm\",\n                children: \"Forgot your love password? \\uD83D\\uDCAD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: loading,\n              className: \"w-full btn-love py-3 rounded-xl font-bold text-base transition-all duration-300 disabled:opacity-70 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-amber-800 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Unlocking hearts...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: [/*#__PURE__*/_jsxDEV(Heart, {\n                  className: \"mr-2 animate-pulse\",\n                  size: 18\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 23\n                }, this), \"Enter Love Sanctuary\", /*#__PURE__*/_jsxDEV(Sparkles, {\n                  className: \"ml-2 animate-pulse\",\n                  size: 18\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this), !showForgotPassword && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-amber-100 to-amber-200 border border-amber-300 rounded-xl p-3 shadow-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-bold text-amber-900 mb-1 flex items-center justify-center text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(Sparkles, {\n                  size: 14,\n                  className: \"mr-1 text-amber-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 21\n                }, this), \"Demo Love Portal\", /*#__PURE__*/_jsxDEV(Sparkles, {\n                  size: 14,\n                  className: \"ml-1 text-amber-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 355,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 352,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-amber-800 font-mono bg-white px-2 py-1 rounded-lg inline-block\",\n                children: \"<EMAIL> \\u2022 mazzalin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 357,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 351,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-amber-800 font-medium text-base drop-shadow-lg\",\n            children: \"\\uD83D\\uDC95 Where hearts connect digitally \\uD83D\\uDC95\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(Login, \"/Mdna7JMc3h93bJU3J1bsVqDHsM=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Heart", "Eye", "Eye<PERSON>ff", "Mail", "Lock", "<PERSON><PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "credentials", "setCredentials", "email", "password", "error", "setError", "loading", "setLoading", "showPassword", "setShowPassword", "showForgotPassword", "setShowForgotPassword", "mousePosition", "setMousePosition", "x", "y", "login", "handleMouseMove", "e", "clientX", "window", "innerWidth", "clientY", "innerHeight", "addEventListener", "removeEventListener", "handleSubmit", "preventDefault", "result", "success", "message", "console", "handleForgotPassword", "FloatingElement", "children", "delay", "duration", "className", "style", "left", "Math", "random", "top", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "Array", "map", "_", "i", "animationDelay", "animationDuration", "size", "onClick", "onSubmit", "type", "required", "value", "onChange", "target", "placeholder", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Heart, Eye, EyeOff, Mail, Lock, Sparkles } from 'lucide-react';\nimport { useAuth } from '../context/AuthContext';\n\nconst Login = () => {\n  const [credentials, setCredentials] = useState({ email: '', password: '' });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [showPassword, setShowPassword] = useState(false);\n  const [showForgotPassword, setShowForgotPassword] = useState(false);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const { login } = useAuth();\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      setMousePosition({\n        x: (e.clientX / window.innerWidth) * 100,\n        y: (e.clientY / window.innerHeight) * 100,\n      });\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const result = await login(credentials);\n      if (!result.success) {\n        setError(result.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n      setError('An unexpected error occurred');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleForgotPassword = () => {\n    setShowForgotPassword(true);\n    setError('');\n  };\n\n  const FloatingElement = ({ children, delay = 0, duration = 6 }) => (\n    <div\n      className=\"absolute opacity-20 pointer-events-none select-none\"\n      style={{\n        left: `${Math.random() * 90 + 5}%`,\n        top: `${Math.random() * 90 + 5}%`,\n        animation: `float ${duration}s ease-in-out infinite ${delay}s`,\n      }}\n    >\n      {children}\n    </div>\n  );\n\n  return (\n    <>\n      <style jsx>{`\n        @keyframes float {\n          0%, 100% { transform: translateY(0px) rotate(0deg); }\n          33% { transform: translateY(-20px) rotate(5deg); }\n          66% { transform: translateY(10px) rotate(-3deg); }\n        }\n        \n        @keyframes pulse-glow {\n          0%, 100% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); }\n          50% { box-shadow: 0 0 40px rgba(212, 175, 55, 0.6), 0 0 60px rgba(212, 175, 55, 0.3); }\n        }\n        \n        @keyframes gradient-shift {\n          0% { background-position: 0% 50%; }\n          50% { background-position: 100% 50%; }\n          100% { background-position: 0% 50%; }\n        }\n        \n        @keyframes heartbeat {\n          0%, 100% { transform: scale(1); }\n          25% { transform: scale(1.1); }\n          50% { transform: scale(1.05); }\n          75% { transform: scale(1.15); }\n        }\n        \n        @keyframes shimmer {\n          0% { transform: translateX(-100%); }\n          100% { transform: translateX(100%); }\n        }\n        \n        .love-gradient {\n          background: linear-gradient(\n            135deg,\n            #f7f3f0 0%,\n            #e8d5d5 25%,\n            #d4c4b0 50%,\n            #c4b59f 75%,\n            #b8a9c9 100%\n          );\n          background-size: 400% 400%;\n          animation: gradient-shift 8s ease infinite;\n        }\n        \n        .glass-card {\n          background: rgba(247, 243, 240, 0.85);\n          backdrop-filter: blur(20px);\n          border: 1px solid rgba(212, 175, 55, 0.2);\n          box-shadow: \n            0 25px 50px -12px rgba(44, 24, 16, 0.15),\n            0 0 0 1px rgba(212, 175, 55, 0.1) inset;\n        }\n        \n        .input-glow:focus {\n          animation: pulse-glow 2s ease-in-out infinite;\n        }\n        \n        .btn-love {\n          background: linear-gradient(135deg, #d4af37, #e6c547, #d4af37);\n          background-size: 200% 200%;\n          position: relative;\n          overflow: hidden;\n          color: #2c1810;\n        }\n        \n        .btn-love::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: -100%;\n          width: 100%;\n          height: 100%;\n          background: linear-gradient(\n            90deg,\n            transparent,\n            rgba(255, 255, 255, 0.4),\n            transparent\n          );\n          transition: left 0.5s;\n        }\n        \n        .btn-love:hover::before {\n          left: 100%;\n        }\n        \n        .btn-love:hover {\n          background-position: 100% 0;\n          transform: translateY(-2px);\n          box-shadow: 0 10px 25px rgba(212, 175, 55, 0.4);\n        }\n        \n        .floating-particles {\n          position: absolute;\n          width: 100%;\n          height: 100%;\n          overflow: hidden;\n          pointer-events: none;\n        }\n        \n        .particle {\n          position: absolute;\n          width: 4px;\n          height: 4px;\n          background: rgba(212, 175, 55, 0.6);\n          border-radius: 50%;\n          animation: float 8s linear infinite;\n        }\n        \n        .interactive-bg {\n          background: radial-gradient(\n            circle at ${mousePosition.x}% ${mousePosition.y}%,\n            rgba(212, 175, 55, 0.2) 0%,\n            transparent 50%\n          );\n          transition: background 0.3s ease;\n        }\n      `}</style>\n      \n      <div className=\"h-screen flex items-center justify-center love-gradient relative overflow-hidden\">\n        {/* Interactive Background */}\n        <div className=\"absolute inset-0 interactive-bg\" />\n        \n        {/* Floating Particles */}\n        <div className=\"floating-particles\">\n          {[...Array(10)].map((_, i) => (\n            <div\n              key={i}\n              className=\"particle\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                animationDelay: `${i * 0.5}s`,\n                animationDuration: `${5 + Math.random() * 3}s`,\n              }}\n            />\n          ))}\n        </div>\n        \n        {/* Floating Romantic Elements - Reduced */}\n        <FloatingElement delay={0} duration={5}>\n          <Heart size={20} className=\"text-amber-400\" />\n        </FloatingElement>\n        <FloatingElement delay={1} duration={7}>\n          <Sparkles size={16} className=\"text-amber-300\" />\n        </FloatingElement>\n        <FloatingElement delay={2} duration={6}>\n          <div className=\"text-xl\">💖</div>\n        </FloatingElement>\n        <FloatingElement delay={3} duration={8}>\n          <div className=\"text-lg\">✨</div>\n        </FloatingElement>\n\n        {/* Main Login Card - Compact */}\n        <div className=\"max-w-sm w-full mx-4 relative z-10\">\n          <div className=\"glass-card rounded-2xl p-6 transform hover:scale-[1.02] transition-all duration-500\">\n            \n            {/* Header Section - Compact */}\n            <div className=\"text-center mb-6\">\n              <div \n                className=\"text-5xl mb-3 inline-block\"\n                style={{ animation: 'heartbeat 2s ease-in-out infinite' }}\n              >\n                💝\n              </div>\n              <h1 className=\"text-3xl font-bold bg-gradient-to-r from-amber-600 via-amber-700 to-amber-800 bg-clip-text text-transparent mb-1\">\n                {showForgotPassword ? 'Recovery' : 'Love Awaits'}\n              </h1>\n              <p className=\"text-amber-800 text-base font-medium\">\n                {showForgotPassword ? 'Your special credentials' : 'Enter your heart\\'s sanctuary'}\n              </p>\n            </div>\n\n            {showForgotPassword ? (\n              // Forgot Password View - Compact\n              <div className=\"space-y-4 animate-in slide-in-from-right duration-500\">\n                <div className=\"bg-gradient-to-r from-amber-50 to-amber-100 border border-amber-300 rounded-xl p-4 shadow-lg\">\n                  <div className=\"flex items-center mb-2\">\n                    <Heart className=\"text-amber-600 mr-2\" size={16} />\n                    <p className=\"font-bold text-amber-900 text-sm\">Your Love Credentials:</p>\n                  </div>\n                  <div className=\"space-y-2 text-amber-800 text-sm\">\n                    <p className=\"flex items-center\">\n                      <Mail size={14} className=\"mr-2 text-amber-600\" />\n                      <span className=\"font-mono bg-white px-2 py-1 rounded text-xs\"><EMAIL></span>\n                    </p>\n                    <p className=\"flex items-center\">\n                      <Lock size={14} className=\"mr-2 text-amber-600\" />\n                      <span className=\"font-mono bg-white px-2 py-1 rounded text-xs\">mazzalin</span>\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => setShowForgotPassword(false)}\n                  className=\"w-full btn-love py-3 rounded-xl font-bold text-base transition-all duration-300 hover:shadow-xl\"\n                >\n                  ← Return to Love\n                </button>\n              </div>\n            ) : (\n              // Login Form - Compact\n              <form onSubmit={handleSubmit} className=\"space-y-4\">\n                {error && (\n                  <div className=\"bg-gradient-to-r from-red-100 to-pink-100 border border-red-300 text-red-700 px-4 py-3 rounded-xl shadow-lg animate-in slide-in-from-top duration-300\">\n                    <div className=\"flex items-center\">\n                      <Heart className=\"text-red-500 mr-2 flex-shrink-0\" size={16} />\n                      <span className=\"font-medium text-sm\">{error}</span>\n                    </div>\n                  </div>\n                )}\n                \n                {/* Email Input - Compact */}\n                <div className=\"space-y-1\">\n                  <label className=\"block text-xs font-bold text-amber-800 mb-1 flex items-center\">\n                    <Mail size={14} className=\"mr-1 text-amber-600\" />\n                    Email Address\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type=\"email\"\n                      required\n                      className=\"w-full px-4 py-3 bg-white/70 border border-amber-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-200 focus:border-amber-500 input-glow transition-all duration-300 text-amber-900 placeholder-amber-600 text-sm\"\n                      value={credentials.email}\n                      onChange={(e) => setCredentials({...credentials, email: e.target.value})}\n                      placeholder=\"Enter your heart's email...\"\n                    />\n                  </div>\n                </div>\n                \n                {/* Password Input - Compact */}\n                <div className=\"space-y-1\">\n                  <label className=\"block text-xs font-bold text-amber-800 mb-1 flex items-center\">\n                    <Lock size={14} className=\"mr-1 text-amber-600\" />\n                    Password\n                  </label>\n                  <div className=\"relative\">\n                    <input\n                      type={showPassword ? \"text\" : \"password\"}\n                      required\n                      className=\"w-full px-4 py-3 pr-12 bg-white/70 border border-amber-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-200 focus:border-amber-500 input-glow transition-all duration-300 text-amber-900 placeholder-amber-600 text-sm\"\n                      value={credentials.password}\n                      onChange={(e) => setCredentials({...credentials, password: e.target.value})}\n                      placeholder=\"Your secret love code...\"\n                    />\n                    <button\n                      type=\"button\"\n                      className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-amber-600 hover:text-amber-700 transition-colors duration-200 p-1\"\n                      onClick={() => setShowPassword(!showPassword)}\n                    >\n                      {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}\n                    </button>\n                  </div>\n                </div>\n                \n                {/* Forgot Password Link - Compact */}\n                <div className=\"text-center\">\n                  <button\n                    type=\"button\"\n                    onClick={handleForgotPassword}\n                    className=\"text-amber-700 hover:text-amber-600 font-medium transition-colors duration-200 hover:underline text-sm\"\n                  >\n                    Forgot your love password? 💭\n                  </button>\n                </div>\n                \n                {/* Submit Button - Compact */}\n                <button\n                  type=\"submit\"\n                  disabled={loading}\n                  className=\"w-full btn-love py-3 rounded-xl font-bold text-base transition-all duration-300 disabled:opacity-70 disabled:cursor-not-allowed shadow-lg hover:shadow-xl\"\n                >\n                  {loading ? (\n                    <div className=\"flex items-center justify-center\">\n                      <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-amber-800 mr-2\"></div>\n                      <span>Unlocking hearts...</span>\n                    </div>\n                  ) : (\n                    <div className=\"flex items-center justify-center\">\n                      <Heart className=\"mr-2 animate-pulse\" size={18} />\n                      Enter Love Sanctuary\n                      <Sparkles className=\"ml-2 animate-pulse\" size={18} />\n                    </div>\n                  )}\n                </button>\n              </form>\n            )}\n            \n            {/* Demo Credentials - Compact */}\n            {!showForgotPassword && (\n              <div className=\"mt-4 text-center\">\n                <div className=\"bg-gradient-to-r from-amber-100 to-amber-200 border border-amber-300 rounded-xl p-3 shadow-lg\">\n                  <p className=\"font-bold text-amber-900 mb-1 flex items-center justify-center text-sm\">\n                    <Sparkles size={14} className=\"mr-1 text-amber-600\" />\n                    Demo Love Portal\n                    <Sparkles size={14} className=\"ml-1 text-amber-600\" />\n                  </p>\n                  <p className=\"text-xs text-amber-800 font-mono bg-white px-2 py-1 rounded-lg inline-block\">\n                    <EMAIL> • mazzalin\n                  </p>\n                </div>\n              </div>\n            )}\n            \n          </div>\n          \n          {/* Bottom decorative text - Compact */}\n          <div className=\"text-center mt-4\">\n            <p className=\"text-amber-800 font-medium text-base drop-shadow-lg\">\n              💕 Where hearts connect digitally 💕\n            </p>\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Login;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,cAAc;AACvE,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEjD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC;IAAEiB,KAAK,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC3E,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC2B,aAAa,EAAEC,gBAAgB,CAAC,GAAG5B,QAAQ,CAAC;IAAE6B,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAClE,MAAM;IAAEC;EAAM,CAAC,GAAGvB,OAAO,CAAC,CAAC;EAE3BP,SAAS,CAAC,MAAM;IACd,MAAM+B,eAAe,GAAIC,CAAC,IAAK;MAC7BL,gBAAgB,CAAC;QACfC,CAAC,EAAGI,CAAC,CAACC,OAAO,GAAGC,MAAM,CAACC,UAAU,GAAI,GAAG;QACxCN,CAAC,EAAGG,CAAC,CAACI,OAAO,GAAGF,MAAM,CAACG,WAAW,GAAI;MACxC,CAAC,CAAC;IACJ,CAAC;IAEDH,MAAM,CAACI,gBAAgB,CAAC,WAAW,EAAEP,eAAe,CAAC;IACrD,OAAO,MAAMG,MAAM,CAACK,mBAAmB,CAAC,WAAW,EAAER,eAAe,CAAC;EACvE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,YAAY,GAAG,MAAOR,CAAC,IAAK;IAChCA,CAAC,CAACS,cAAc,CAAC,CAAC;IAClBpB,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMuB,MAAM,GAAG,MAAMZ,KAAK,CAAChB,WAAW,CAAC;MACvC,IAAI,CAAC4B,MAAM,CAACC,OAAO,EAAE;QACnBxB,QAAQ,CAACuB,MAAM,CAACE,OAAO,IAAI,cAAc,CAAC;MAC5C;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpCC,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,oBAAoB,GAAGA,CAAA,KAAM;IACjCrB,qBAAqB,CAAC,IAAI,CAAC;IAC3BN,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,MAAM4B,eAAe,GAAGA,CAAC;IAAEC,QAAQ;IAAEC,KAAK,GAAG,CAAC;IAAEC,QAAQ,GAAG;EAAE,CAAC,kBAC5DzC,OAAA;IACE0C,SAAS,EAAC,qDAAqD;IAC/DC,KAAK,EAAE;MACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG;MAClCC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG;MACjCE,SAAS,EAAE,SAASP,QAAQ,0BAA0BD,KAAK;IAC7D,CAAE;IAAAD,QAAA,EAEDA;EAAQ;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CACN;EAED,oBACEpD,OAAA,CAAAE,SAAA;IAAAqC,QAAA,gBACEvC,OAAA;MAAOqD,GAAG;MAAAd,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwBtB,aAAa,CAACE,CAAC,KAAKF,aAAa,CAACG,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA;IAAO;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEVpD,OAAA;MAAK0C,SAAS,EAAC,kFAAkF;MAAAH,QAAA,gBAE/FvC,OAAA;QAAK0C,SAAS,EAAC;MAAiC;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGnDpD,OAAA;QAAK0C,SAAS,EAAC,oBAAoB;QAAAH,QAAA,EAChC,CAAC,GAAGe,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACvBzD,OAAA;UAEE0C,SAAS,EAAC,UAAU;UACpBC,KAAK,EAAE;YACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;YAC/BY,cAAc,EAAE,GAAGD,CAAC,GAAG,GAAG,GAAG;YAC7BE,iBAAiB,EAAE,GAAG,CAAC,GAAGd,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC;UAC7C;QAAE,GANGW,CAAC;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOP,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNpD,OAAA,CAACsC,eAAe;QAACE,KAAK,EAAE,CAAE;QAACC,QAAQ,EAAE,CAAE;QAAAF,QAAA,eACrCvC,OAAA,CAACR,KAAK;UAACoE,IAAI,EAAE,EAAG;UAAClB,SAAS,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAClBpD,OAAA,CAACsC,eAAe;QAACE,KAAK,EAAE,CAAE;QAACC,QAAQ,EAAE,CAAE;QAAAF,QAAA,eACrCvC,OAAA,CAACH,QAAQ;UAAC+D,IAAI,EAAE,EAAG;UAAClB,SAAS,EAAC;QAAgB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAClBpD,OAAA,CAACsC,eAAe;QAACE,KAAK,EAAE,CAAE;QAACC,QAAQ,EAAE,CAAE;QAAAF,QAAA,eACrCvC,OAAA;UAAK0C,SAAS,EAAC,SAAS;UAAAH,QAAA,EAAC;QAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAClBpD,OAAA,CAACsC,eAAe;QAACE,KAAK,EAAE,CAAE;QAACC,QAAQ,EAAE,CAAE;QAAAF,QAAA,eACrCvC,OAAA;UAAK0C,SAAS,EAAC,SAAS;UAAAH,QAAA,EAAC;QAAC;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGlBpD,OAAA;QAAK0C,SAAS,EAAC,oCAAoC;QAAAH,QAAA,gBACjDvC,OAAA;UAAK0C,SAAS,EAAC,qFAAqF;UAAAH,QAAA,gBAGlGvC,OAAA;YAAK0C,SAAS,EAAC,kBAAkB;YAAAH,QAAA,gBAC/BvC,OAAA;cACE0C,SAAS,EAAC,4BAA4B;cACtCC,KAAK,EAAE;gBAAEK,SAAS,EAAE;cAAoC,CAAE;cAAAT,QAAA,EAC3D;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpD,OAAA;cAAI0C,SAAS,EAAC,kHAAkH;cAAAH,QAAA,EAC7HxB,kBAAkB,GAAG,UAAU,GAAG;YAAa;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,eACLpD,OAAA;cAAG0C,SAAS,EAAC,sCAAsC;cAAAH,QAAA,EAChDxB,kBAAkB,GAAG,0BAA0B,GAAG;YAA+B;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAELrC,kBAAkB;UAAA;UACjB;UACAf,OAAA;YAAK0C,SAAS,EAAC,uDAAuD;YAAAH,QAAA,gBACpEvC,OAAA;cAAK0C,SAAS,EAAC,8FAA8F;cAAAH,QAAA,gBAC3GvC,OAAA;gBAAK0C,SAAS,EAAC,wBAAwB;gBAAAH,QAAA,gBACrCvC,OAAA,CAACR,KAAK;kBAACkD,SAAS,EAAC,qBAAqB;kBAACkB,IAAI,EAAE;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnDpD,OAAA;kBAAG0C,SAAS,EAAC,kCAAkC;kBAAAH,QAAA,EAAC;gBAAsB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvE,CAAC,eACNpD,OAAA;gBAAK0C,SAAS,EAAC,kCAAkC;gBAAAH,QAAA,gBAC/CvC,OAAA;kBAAG0C,SAAS,EAAC,mBAAmB;kBAAAH,QAAA,gBAC9BvC,OAAA,CAACL,IAAI;oBAACiE,IAAI,EAAE,EAAG;oBAAClB,SAAS,EAAC;kBAAqB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDpD,OAAA;oBAAM0C,SAAS,EAAC,8CAA8C;oBAAAH,QAAA,EAAC;kBAAoB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzF,CAAC,eACJpD,OAAA;kBAAG0C,SAAS,EAAC,mBAAmB;kBAAAH,QAAA,gBAC9BvC,OAAA,CAACJ,IAAI;oBAACgE,IAAI,EAAE,EAAG;oBAAClB,SAAS,EAAC;kBAAqB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClDpD,OAAA;oBAAM0C,SAAS,EAAC,8CAA8C;oBAAAH,QAAA,EAAC;kBAAQ;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNpD,OAAA;cACE6D,OAAO,EAAEA,CAAA,KAAM7C,qBAAqB,CAAC,KAAK,CAAE;cAC5C0B,SAAS,EAAC,iGAAiG;cAAAH,QAAA,EAC5G;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;UAAA;UAEN;UACApD,OAAA;YAAM8D,QAAQ,EAAE/B,YAAa;YAACW,SAAS,EAAC,WAAW;YAAAH,QAAA,GAChD9B,KAAK,iBACJT,OAAA;cAAK0C,SAAS,EAAC,uJAAuJ;cAAAH,QAAA,eACpKvC,OAAA;gBAAK0C,SAAS,EAAC,mBAAmB;gBAAAH,QAAA,gBAChCvC,OAAA,CAACR,KAAK;kBAACkD,SAAS,EAAC,iCAAiC;kBAACkB,IAAI,EAAE;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/DpD,OAAA;kBAAM0C,SAAS,EAAC,qBAAqB;kBAAAH,QAAA,EAAE9B;gBAAK;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN,eAGDpD,OAAA;cAAK0C,SAAS,EAAC,WAAW;cAAAH,QAAA,gBACxBvC,OAAA;gBAAO0C,SAAS,EAAC,+DAA+D;gBAAAH,QAAA,gBAC9EvC,OAAA,CAACL,IAAI;kBAACiE,IAAI,EAAE,EAAG;kBAAClB,SAAS,EAAC;gBAAqB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,iBAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBAAK0C,SAAS,EAAC,UAAU;gBAAAH,QAAA,eACvBvC,OAAA;kBACE+D,IAAI,EAAC,OAAO;kBACZC,QAAQ;kBACRtB,SAAS,EAAC,iOAAiO;kBAC3OuB,KAAK,EAAE5D,WAAW,CAACE,KAAM;kBACzB2D,QAAQ,EAAG3C,CAAC,IAAKjB,cAAc,CAAC;oBAAC,GAAGD,WAAW;oBAAEE,KAAK,EAAEgB,CAAC,CAAC4C,MAAM,CAACF;kBAAK,CAAC,CAAE;kBACzEG,WAAW,EAAC;gBAA6B;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpD,OAAA;cAAK0C,SAAS,EAAC,WAAW;cAAAH,QAAA,gBACxBvC,OAAA;gBAAO0C,SAAS,EAAC,+DAA+D;gBAAAH,QAAA,gBAC9EvC,OAAA,CAACJ,IAAI;kBAACgE,IAAI,EAAE,EAAG;kBAAClB,SAAS,EAAC;gBAAqB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEpD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRpD,OAAA;gBAAK0C,SAAS,EAAC,UAAU;gBAAAH,QAAA,gBACvBvC,OAAA;kBACE+D,IAAI,EAAElD,YAAY,GAAG,MAAM,GAAG,UAAW;kBACzCmD,QAAQ;kBACRtB,SAAS,EAAC,uOAAuO;kBACjPuB,KAAK,EAAE5D,WAAW,CAACG,QAAS;kBAC5B0D,QAAQ,EAAG3C,CAAC,IAAKjB,cAAc,CAAC;oBAAC,GAAGD,WAAW;oBAAEG,QAAQ,EAAEe,CAAC,CAAC4C,MAAM,CAACF;kBAAK,CAAC,CAAE;kBAC5EG,WAAW,EAAC;gBAA0B;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACFpD,OAAA;kBACE+D,IAAI,EAAC,QAAQ;kBACbrB,SAAS,EAAC,4HAA4H;kBACtImB,OAAO,EAAEA,CAAA,KAAM/C,eAAe,CAAC,CAACD,YAAY,CAAE;kBAAA0B,QAAA,EAE7C1B,YAAY,gBAAGb,OAAA,CAACN,MAAM;oBAACkE,IAAI,EAAE;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGpD,OAAA,CAACP,GAAG;oBAACmE,IAAI,EAAE;kBAAG;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNpD,OAAA;cAAK0C,SAAS,EAAC,aAAa;cAAAH,QAAA,eAC1BvC,OAAA;gBACE+D,IAAI,EAAC,QAAQ;gBACbF,OAAO,EAAExB,oBAAqB;gBAC9BK,SAAS,EAAC,wGAAwG;gBAAAH,QAAA,EACnH;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGNpD,OAAA;cACE+D,IAAI,EAAC,QAAQ;cACbM,QAAQ,EAAE1D,OAAQ;cAClB+B,SAAS,EAAC,2JAA2J;cAAAH,QAAA,EAEpK5B,OAAO,gBACNX,OAAA;gBAAK0C,SAAS,EAAC,kCAAkC;gBAAAH,QAAA,gBAC/CvC,OAAA;kBAAK0C,SAAS,EAAC;gBAAoE;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1FpD,OAAA;kBAAAuC,QAAA,EAAM;gBAAmB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,gBAENpD,OAAA;gBAAK0C,SAAS,EAAC,kCAAkC;gBAAAH,QAAA,gBAC/CvC,OAAA,CAACR,KAAK;kBAACkD,SAAS,EAAC,oBAAoB;kBAACkB,IAAI,EAAE;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,wBAElD,eAAApD,OAAA,CAACH,QAAQ;kBAAC6C,SAAS,EAAC,oBAAoB;kBAACkB,IAAI,EAAE;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACP,EAGA,CAACrC,kBAAkB,iBAClBf,OAAA;YAAK0C,SAAS,EAAC,kBAAkB;YAAAH,QAAA,eAC/BvC,OAAA;cAAK0C,SAAS,EAAC,+FAA+F;cAAAH,QAAA,gBAC5GvC,OAAA;gBAAG0C,SAAS,EAAC,wEAAwE;gBAAAH,QAAA,gBACnFvC,OAAA,CAACH,QAAQ;kBAAC+D,IAAI,EAAE,EAAG;kBAAClB,SAAS,EAAC;gBAAqB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,oBAEtD,eAAApD,OAAA,CAACH,QAAQ;kBAAC+D,IAAI,EAAE,EAAG;kBAAClB,SAAS,EAAC;gBAAqB;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACJpD,OAAA;gBAAG0C,SAAS,EAAC,6EAA6E;gBAAAH,QAAA,EAAC;cAE3F;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEE,CAAC,eAGNpD,OAAA;UAAK0C,SAAS,EAAC,kBAAkB;UAAAH,QAAA,eAC/BvC,OAAA;YAAG0C,SAAS,EAAC,qDAAqD;YAAAH,QAAA,EAAC;UAEnE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAAChD,EAAA,CAnXID,KAAK;EAAA,QAOSL,OAAO;AAAA;AAAAwE,EAAA,GAPrBnE,KAAK;AAqXX,eAAeA,KAAK;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
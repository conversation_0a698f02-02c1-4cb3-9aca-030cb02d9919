{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\SettingsPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Settings, X, User, Lock, Moon, Sun, Bell, Shield, Heart, Palette, Volume2, VolumeX, Eye, EyeOff, Camera, Download, Trash2, LogOut, Save } from 'lucide-react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SettingsPanel = ({\n  isOpen,\n  onClose\n}) => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const {\n    darkMode,\n    toggleDarkMode\n  } = useTheme();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [notifications, setNotifications] = useState(true);\n  const [soundEnabled, setSoundEnabled] = useState(true);\n  const [privateMode, setPrivateMode] = useState(false);\n  const [showPasswords, setShowPasswords] = useState(false);\n  const [profileData, setProfileData] = useState({\n    name: (user === null || user === void 0 ? void 0 : user.name) || '',\n    email: (user === null || user === void 0 ? void 0 : user.email) || '',\n    bio: 'Living our love story ✨',\n    anniversary: '2024-01-01'\n  });\n  const [passwordData, setPasswordData] = useState({\n    current: '',\n    new: '',\n    confirm: ''\n  });\n  const tabs = [{\n    id: 'profile',\n    name: 'Profile',\n    icon: User,\n    color: 'from-blue-500 to-blue-600'\n  }, {\n    id: 'security',\n    name: 'Security',\n    icon: Shield,\n    color: 'from-green-500 to-green-600'\n  }, {\n    id: 'appearance',\n    name: 'Appearance',\n    icon: Palette,\n    color: 'from-purple-500 to-purple-600'\n  }, {\n    id: 'privacy',\n    name: 'Privacy',\n    icon: Eye,\n    color: 'from-orange-500 to-orange-600'\n  }, {\n    id: 'notifications',\n    name: 'Notifications',\n    icon: Bell,\n    color: 'from-pink-500 to-pink-600'\n  }];\n  const handleSaveProfile = () => {\n    console.log('Saving profile:', profileData);\n  };\n  const handleChangePassword = () => {\n    console.log('Changing password');\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 z-50 overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300\",\n      onClick: onClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 shadow-2xl transform transition-transform duration-300 ease-out\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-pink-500 to-purple-600 p-6 text-white\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                className: \"text-white\",\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold\",\n              children: \"Settings\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"p-2 hover:bg-white/20 rounded-full transition-colors\",\n            children: /*#__PURE__*/_jsxDEV(X, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full flex items-center justify-center text-xl\",\n            children: \"\\uD83D\\uDC95\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"font-semibold\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-white/80 text-sm\",\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex overflow-x-auto bg-gray-50 dark:bg-gray-800 px-4 py-2 space-x-2\",\n        children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setActiveTab(tab.id),\n          className: `flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all ${activeTab === tab.id ? `bg-gradient-to-r ${tab.color} text-white shadow-lg` : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'}`,\n          children: [/*#__PURE__*/_jsxDEV(tab.icon, {\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: tab.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 15\n          }, this)]\n        }, tab.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 overflow-y-auto p-6 dark:bg-gray-900\",\n        children: [activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-800 dark:text-white flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              className: \"text-blue-500\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 17\n            }, this), \"Profile Information\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                value: profileData.name,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  name: e.target.value\n                }),\n                className: \"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                value: profileData.email,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  email: e.target.value\n                }),\n                className: \"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Bio\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: profileData.bio,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  bio: e.target.value\n                }),\n                rows: 3,\n                className: \"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                children: \"Anniversary Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"date\",\n                value: profileData.anniversary,\n                onChange: e => setProfileData({\n                  ...profileData,\n                  anniversary: e.target.value\n                }),\n                className: \"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSaveProfile,\n              className: \"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Save, {\n                size: 18\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), \"Save Changes\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 13\n        }, this), activeTab === 'security' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-800 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              className: \"text-green-500\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), \"Security Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Current Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: showPasswords ? \"text\" : \"password\",\n                  value: passwordData.current,\n                  onChange: e => setPasswordData({\n                    ...passwordData,\n                    current: e.target.value\n                  }),\n                  className: \"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => setShowPasswords(!showPasswords),\n                  className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\",\n                  children: showPasswords ? /*#__PURE__*/_jsxDEV(EyeOff, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 40\n                  }, this) : /*#__PURE__*/_jsxDEV(Eye, {\n                    size: 18\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 63\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPasswords ? \"text\" : \"password\",\n                value: passwordData.new,\n                onChange: e => setPasswordData({\n                  ...passwordData,\n                  new: e.target.value\n                }),\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Confirm New Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPasswords ? \"text\" : \"password\",\n                value: passwordData.confirm,\n                onChange: e => setPasswordData({\n                  ...passwordData,\n                  confirm: e.target.value\n                }),\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleChangePassword,\n              className: \"w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all flex items-center justify-center gap-2\",\n              children: [/*#__PURE__*/_jsxDEV(Lock, {\n                size: 18\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this), \"Change Password\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), activeTab === 'appearance' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-800 dark:text-white flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Palette, {\n              className: \"text-purple-500\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), \"Appearance\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [darkMode ? /*#__PURE__*/_jsxDEV(Moon, {\n                  className: \"text-purple-500\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 33\n                }, this) : /*#__PURE__*/_jsxDEV(Sun, {\n                  className: \"text-yellow-500\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 82\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-800 dark:text-white\",\n                    children: \"Dark Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                    children: \"Switch to dark theme\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: toggleDarkMode,\n                className: `relative w-12 h-6 rounded-full transition-colors ${darkMode ? 'bg-purple-500' : 'bg-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${darkMode ? 'translate-x-6' : 'translate-x-0.5'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-medium text-gray-800 dark:text-white mb-3\",\n                children: \"Theme Colors\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-4 gap-3\",\n                children: ['pink', 'purple', 'blue', 'green'].map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `w-12 h-12 rounded-full bg-gradient-to-r from-${color}-400 to-${color}-600 hover:scale-110 transition-transform`\n                }, color, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 233,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this), activeTab === 'privacy' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-800 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Eye, {\n              className: \"text-orange-500\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 17\n            }, this), \"Privacy Settings\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Shield, {\n                  className: \"text-orange-500\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-800\",\n                    children: \"Private Mode\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Hide your activity status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setPrivateMode(!privateMode),\n                className: `relative w-12 h-6 rounded-full transition-colors ${privateMode ? 'bg-orange-500' : 'bg-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${privateMode ? 'translate-x-6' : 'translate-x-0.5'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 277,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 13\n        }, this), activeTab === 'notifications' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-bold text-gray-800 flex items-center gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(Bell, {\n              className: \"text-pink-500\",\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 17\n            }, this), \"Notifications\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [/*#__PURE__*/_jsxDEV(Bell, {\n                  className: \"text-pink-500\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-800\",\n                    children: \"Push Notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Receive notifications for new messages\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setNotifications(!notifications),\n                className: `relative w-12 h-6 rounded-full transition-colors ${notifications ? 'bg-pink-500' : 'bg-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${notifications ? 'translate-x-6' : 'translate-x-0.5'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3\",\n                children: [soundEnabled ? /*#__PURE__*/_jsxDEV(Volume2, {\n                  className: \"text-pink-500\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 37\n                }, this) : /*#__PURE__*/_jsxDEV(VolumeX, {\n                  className: \"text-gray-400\",\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 87\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-800\",\n                    children: \"Sound Effects\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: \"Play sounds for interactions\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => setSoundEnabled(!soundEnabled),\n                className: `relative w-12 h-6 rounded-full transition-colors ${soundEnabled ? 'bg-pink-500' : 'bg-gray-300'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${soundEnabled ? 'translate-x-6' : 'translate-x-0.5'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-t border-gray-200 dark:border-gray-700 p-6 dark:bg-gray-900\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: logout,\n          className: \"w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 rounded-lg font-medium hover:from-red-600 hover:to-red-700 transition-all flex items-center justify-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(LogOut, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this), \"Sign Out\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(SettingsPanel, \"AiEidv563Oy1zlsS59eZdTVqQ0c=\", false, function () {\n  return [useAuth, useTheme];\n});\n_c = SettingsPanel;\nexport default SettingsPanel;\nvar _c;\n$RefreshReg$(_c, \"SettingsPanel\");", "map": {"version": 3, "names": ["React", "useState", "Settings", "X", "User", "Lock", "Moon", "Sun", "Bell", "Shield", "Heart", "Palette", "Volume2", "VolumeX", "Eye", "Eye<PERSON>ff", "Camera", "Download", "Trash2", "LogOut", "Save", "useAuth", "useTheme", "jsxDEV", "_jsxDEV", "SettingsPanel", "isOpen", "onClose", "_s", "user", "logout", "darkMode", "toggleDarkMode", "activeTab", "setActiveTab", "notifications", "setNotifications", "soundEnabled", "setSoundEnabled", "privateMode", "setPrivateMode", "showPasswords", "setShowPasswords", "profileData", "setProfileData", "name", "email", "bio", "anniversary", "passwordData", "setPasswordData", "current", "new", "confirm", "tabs", "id", "icon", "color", "handleSaveProfile", "console", "log", "handleChangePassword", "className", "children", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "map", "tab", "type", "value", "onChange", "e", "target", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/SettingsPanel.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  <PERSON>ting<PERSON>, X, User, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, \n  Heart, Palette, Volume2, VolumeX, Eye, EyeOff,\n  Camera, Download, Trash2, LogOut, Save\n} from 'lucide-react';\nimport { useAuth } from '../context/AuthContext';\nimport { useTheme } from '../context/ThemeContext';\n\nconst SettingsPanel = ({ isOpen, onClose }) => {\n  const { user, logout } = useAuth();\n  const { darkMode, toggleDarkMode } = useTheme();\n  const [activeTab, setActiveTab] = useState('profile');\n  const [notifications, setNotifications] = useState(true);\n  const [soundEnabled, setSoundEnabled] = useState(true);\n  const [privateMode, setPrivateMode] = useState(false);\n  const [showPasswords, setShowPasswords] = useState(false);\n  \n  const [profileData, setProfileData] = useState({\n    name: user?.name || '',\n    email: user?.email || '',\n    bio: 'Living our love story ✨',\n    anniversary: '2024-01-01'\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    current: '',\n    new: '',\n    confirm: ''\n  });\n\n  const tabs = [\n    { id: 'profile', name: 'Profile', icon: User, color: 'from-blue-500 to-blue-600' },\n    { id: 'security', name: 'Security', icon: Shield, color: 'from-green-500 to-green-600' },\n    { id: 'appearance', name: 'Appearance', icon: Palette, color: 'from-purple-500 to-purple-600' },\n    { id: 'privacy', name: 'Privacy', icon: Eye, color: 'from-orange-500 to-orange-600' },\n    { id: 'notifications', name: 'Notifications', icon: Bell, color: 'from-pink-500 to-pink-600' }\n  ];\n\n  const handleSaveProfile = () => {\n    console.log('Saving profile:', profileData);\n  };\n\n  const handleChangePassword = () => {\n    console.log('Changing password');\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 overflow-hidden\">\n      {/* Backdrop */}\n      <div \n        className=\"absolute inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300\"\n        onClick={onClose}\n      ></div>\n\n      {/* Panel */}\n      <div className=\"absolute right-0 top-0 h-full w-full max-w-md bg-white dark:bg-gray-900 shadow-2xl transform transition-transform duration-300 ease-out\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-pink-500 to-purple-600 p-6 text-white\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\">\n                <Settings className=\"text-white\" size={20} />\n              </div>\n              <h2 className=\"text-2xl font-bold\">Settings</h2>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"p-2 hover:bg-white/20 rounded-full transition-colors\"\n            >\n              <X size={24} />\n            </button>\n          </div>\n\n          {/* User Info */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-12 h-12 bg-gradient-to-r from-pink-400 to-rose-400 rounded-full flex items-center justify-center text-xl\">\n              💕\n            </div>\n            <div>\n              <p className=\"font-semibold\">{user?.name}</p>\n              <p className=\"text-white/80 text-sm\">{user?.email}</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex overflow-x-auto bg-gray-50 dark:bg-gray-800 px-4 py-2 space-x-2\">\n          {tabs.map(tab => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center space-x-2 px-4 py-2 rounded-lg whitespace-nowrap transition-all ${\n                activeTab === tab.id \n                  ? `bg-gradient-to-r ${tab.color} text-white shadow-lg` \n                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'\n              }`}\n            >\n              <tab.icon size={16} />\n              <span className=\"text-sm font-medium\">{tab.name}</span>\n            </button>\n          ))}\n        </div>\n\n        {/* Content */}\n        <div className=\"flex-1 overflow-y-auto p-6 dark:bg-gray-900\">\n          {activeTab === 'profile' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-bold text-gray-800 dark:text-white flex items-center gap-2\">\n                <User className=\"text-blue-500\" size={20} />\n                Profile Information\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Name</label>\n                  <input\n                    type=\"text\"\n                    value={profileData.name}\n                    onChange={(e) => setProfileData({...profileData, name: e.target.value})}\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Email</label>\n                  <input\n                    type=\"email\"\n                    value={profileData.email}\n                    onChange={(e) => setProfileData({...profileData, email: e.target.value})}\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Bio</label>\n                  <textarea\n                    value={profileData.bio}\n                    onChange={(e) => setProfileData({...profileData, bio: e.target.value})}\n                    rows={3}\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Anniversary Date</label>\n                  <input\n                    type=\"date\"\n                    value={profileData.anniversary}\n                    onChange={(e) => setProfileData({...profileData, anniversary: e.target.value})}\n                    className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white\"\n                  />\n                </div>\n                \n                <button\n                  onClick={handleSaveProfile}\n                  className=\"w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all flex items-center justify-center gap-2\"\n                >\n                  <Save size={18} />\n                  Save Changes\n                </button>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'security' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-bold text-gray-800 flex items-center gap-2\">\n                <Shield className=\"text-green-500\" size={20} />\n                Security Settings\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Current Password</label>\n                  <div className=\"relative\">\n                    <input\n                      type={showPasswords ? \"text\" : \"password\"}\n                      value={passwordData.current}\n                      onChange={(e) => setPasswordData({...passwordData, current: e.target.value})}\n                      className=\"w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowPasswords(!showPasswords)}\n                      className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n                    >\n                      {showPasswords ? <EyeOff size={18} /> : <Eye size={18} />}\n                    </button>\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">New Password</label>\n                  <input\n                    type={showPasswords ? \"text\" : \"password\"}\n                    value={passwordData.new}\n                    onChange={(e) => setPasswordData({...passwordData, new: e.target.value})}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Confirm New Password</label>\n                  <input\n                    type={showPasswords ? \"text\" : \"password\"}\n                    value={passwordData.confirm}\n                    onChange={(e) => setPasswordData({...passwordData, confirm: e.target.value})}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500\"\n                  />\n                </div>\n                \n                <button\n                  onClick={handleChangePassword}\n                  className=\"w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-3 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all flex items-center justify-center gap-2\"\n                >\n                  <Lock size={18} />\n                  Change Password\n                </button>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'appearance' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-bold text-gray-800 dark:text-white flex items-center gap-2\">\n                <Palette className=\"text-purple-500\" size={20} />\n                Appearance\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    {darkMode ? <Moon className=\"text-purple-500\" size={20} /> : <Sun className=\"text-yellow-500\" size={20} />}\n                    <div>\n                      <p className=\"font-medium text-gray-800 dark:text-white\">Dark Mode</p>\n                      <p className=\"text-sm text-gray-600 dark:text-gray-400\">Switch to dark theme</p>\n                    </div>\n                  </div>\n                  <button\n                    onClick={toggleDarkMode}\n                    className={`relative w-12 h-6 rounded-full transition-colors ${\n                      darkMode ? 'bg-purple-500' : 'bg-gray-300'\n                    }`}\n                  >\n                    <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${\n                      darkMode ? 'translate-x-6' : 'translate-x-0.5'\n                    }`}></div>\n                  </button>\n                </div>\n                \n                <div className=\"p-4 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <p className=\"font-medium text-gray-800 dark:text-white mb-3\">Theme Colors</p>\n                  <div className=\"grid grid-cols-4 gap-3\">\n                    {['pink', 'purple', 'blue', 'green'].map(color => (\n                      <button\n                        key={color}\n                        className={`w-12 h-12 rounded-full bg-gradient-to-r from-${color}-400 to-${color}-600 hover:scale-110 transition-transform`}\n                      ></button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'privacy' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-bold text-gray-800 flex items-center gap-2\">\n                <Eye className=\"text-orange-500\" size={20} />\n                Privacy Settings\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Shield className=\"text-orange-500\" size={20} />\n                    <div>\n                      <p className=\"font-medium text-gray-800\">Private Mode</p>\n                      <p className=\"text-sm text-gray-600\">Hide your activity status</p>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setPrivateMode(!privateMode)}\n                    className={`relative w-12 h-6 rounded-full transition-colors ${\n                      privateMode ? 'bg-orange-500' : 'bg-gray-300'\n                    }`}\n                  >\n                    <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${\n                      privateMode ? 'translate-x-6' : 'translate-x-0.5'\n                    }`}></div>\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'notifications' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-bold text-gray-800 flex items-center gap-2\">\n                <Bell className=\"text-pink-500\" size={20} />\n                Notifications\n              </h3>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    <Bell className=\"text-pink-500\" size={20} />\n                    <div>\n                      <p className=\"font-medium text-gray-800\">Push Notifications</p>\n                      <p className=\"text-sm text-gray-600\">Receive notifications for new messages</p>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setNotifications(!notifications)}\n                    className={`relative w-12 h-6 rounded-full transition-colors ${\n                      notifications ? 'bg-pink-500' : 'bg-gray-300'\n                    }`}\n                  >\n                    <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${\n                      notifications ? 'translate-x-6' : 'translate-x-0.5'\n                    }`}></div>\n                  </button>\n                </div>\n                \n                <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    {soundEnabled ? <Volume2 className=\"text-pink-500\" size={20} /> : <VolumeX className=\"text-gray-400\" size={20} />}\n                    <div>\n                      <p className=\"font-medium text-gray-800\">Sound Effects</p>\n                      <p className=\"text-sm text-gray-600\">Play sounds for interactions</p>\n                    </div>\n                  </div>\n                  <button\n                    onClick={() => setSoundEnabled(!soundEnabled)}\n                    className={`relative w-12 h-6 rounded-full transition-colors ${\n                      soundEnabled ? 'bg-pink-500' : 'bg-gray-300'\n                    }`}\n                  >\n                    <div className={`absolute w-5 h-5 bg-white rounded-full top-0.5 transition-transform ${\n                      soundEnabled ? 'translate-x-6' : 'translate-x-0.5'\n                    }`}></div>\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Footer */}\n        <div className=\"border-t border-gray-200 dark:border-gray-700 p-6 dark:bg-gray-900\">\n          <button\n            onClick={logout}\n            className=\"w-full bg-gradient-to-r from-red-500 to-red-600 text-white py-3 rounded-lg font-medium hover:from-red-600 hover:to-red-700 transition-all flex items-center justify-center gap-2\"\n          >\n            <LogOut size={18} />\n            Sign Out\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default SettingsPanel;\n\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,QAAQ,EAAEC,CAAC,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAChDC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAC7CC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,IAAI,QACjC,cAAc;AACrB,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,aAAa,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7C,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClC,MAAM;IAAEU,QAAQ;IAAEC;EAAe,CAAC,GAAGV,QAAQ,CAAC,CAAC;EAC/C,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGjC,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAACkC,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAC;IAC7C4C,IAAI,EAAE,CAAAhB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB,IAAI,KAAI,EAAE;IACtBC,KAAK,EAAE,CAAAjB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB,KAAK,KAAI,EAAE;IACxBC,GAAG,EAAE,yBAAyB;IAC9BC,WAAW,EAAE;EACf,CAAC,CAAC;EAEF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC;IAC/CkD,OAAO,EAAE,EAAE;IACXC,GAAG,EAAE,EAAE;IACPC,OAAO,EAAE;EACX,CAAC,CAAC;EAEF,MAAMC,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,SAAS;IAAEV,IAAI,EAAE,SAAS;IAAEW,IAAI,EAAEpD,IAAI;IAAEqD,KAAK,EAAE;EAA4B,CAAC,EAClF;IAAEF,EAAE,EAAE,UAAU;IAAEV,IAAI,EAAE,UAAU;IAAEW,IAAI,EAAE/C,MAAM;IAAEgD,KAAK,EAAE;EAA8B,CAAC,EACxF;IAAEF,EAAE,EAAE,YAAY;IAAEV,IAAI,EAAE,YAAY;IAAEW,IAAI,EAAE7C,OAAO;IAAE8C,KAAK,EAAE;EAAgC,CAAC,EAC/F;IAAEF,EAAE,EAAE,SAAS;IAAEV,IAAI,EAAE,SAAS;IAAEW,IAAI,EAAE1C,GAAG;IAAE2C,KAAK,EAAE;EAAgC,CAAC,EACrF;IAAEF,EAAE,EAAE,eAAe;IAAEV,IAAI,EAAE,eAAe;IAAEW,IAAI,EAAEhD,IAAI;IAAEiD,KAAK,EAAE;EAA4B,CAAC,CAC/F;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEjB,WAAW,CAAC;EAC7C,CAAC;EAED,MAAMkB,oBAAoB,GAAGA,CAAA,KAAM;IACjCF,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;EAClC,CAAC;EAED,IAAI,CAAClC,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKsC,SAAS,EAAC,oCAAoC;IAAAC,QAAA,gBAEjDvC,OAAA;MACEsC,SAAS,EAAC,+EAA+E;MACzFE,OAAO,EAAErC;IAAQ;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAGP5C,OAAA;MAAKsC,SAAS,EAAC,yIAAyI;MAAAC,QAAA,gBAEtJvC,OAAA;QAAKsC,SAAS,EAAC,6DAA6D;QAAAC,QAAA,gBAC1EvC,OAAA;UAAKsC,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDvC,OAAA;YAAKsC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CvC,OAAA;cAAKsC,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFvC,OAAA,CAACtB,QAAQ;gBAAC4D,SAAS,EAAC,YAAY;gBAACO,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN5C,OAAA;cAAIsC,SAAS,EAAC,oBAAoB;cAAAC,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACN5C,OAAA;YACEwC,OAAO,EAAErC,OAAQ;YACjBmC,SAAS,EAAC,sDAAsD;YAAAC,QAAA,eAEhEvC,OAAA,CAACrB,CAAC;cAACkE,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN5C,OAAA;UAAKsC,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CvC,OAAA;YAAKsC,SAAS,EAAC,4GAA4G;YAAAC,QAAA,EAAC;UAE5H;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5C,OAAA;YAAAuC,QAAA,gBACEvC,OAAA;cAAGsC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgB;YAAI;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7C5C,OAAA;cAAGsC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAElC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiB;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN5C,OAAA;QAAKsC,SAAS,EAAC,sEAAsE;QAAAC,QAAA,EAClFT,IAAI,CAACgB,GAAG,CAACC,GAAG,iBACX/C,OAAA;UAEEwC,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAACqC,GAAG,CAAChB,EAAE,CAAE;UACpCO,SAAS,EAAE,qFACT7B,SAAS,KAAKsC,GAAG,CAAChB,EAAE,GAChB,oBAAoBgB,GAAG,CAACd,KAAK,uBAAuB,GACpD,2EAA2E,EAC9E;UAAAM,QAAA,gBAEHvC,OAAA,CAAC+C,GAAG,CAACf,IAAI;YAACa,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtB5C,OAAA;YAAMsC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEQ,GAAG,CAAC1B;UAAI;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATlDG,GAAG,CAAChB,EAAE;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUL,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN5C,OAAA;QAAKsC,SAAS,EAAC,6CAA6C;QAAAC,QAAA,GACzD9B,SAAS,KAAK,SAAS,iBACtBT,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvC,OAAA;YAAIsC,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBACrFvC,OAAA,CAACpB,IAAI;cAAC0D,SAAS,EAAC,eAAe;cAACO,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,uBAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL5C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC/F5C,OAAA;gBACEgD,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE9B,WAAW,CAACE,IAAK;gBACxB6B,QAAQ,EAAGC,CAAC,IAAK/B,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEE,IAAI,EAAE8B,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACxEX,SAAS,EAAC;cAAwL;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAAK;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAChG5C,OAAA;gBACEgD,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE9B,WAAW,CAACG,KAAM;gBACzB4B,QAAQ,EAAGC,CAAC,IAAK/B,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEG,KAAK,EAAE6B,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACzEX,SAAS,EAAC;cAAwL;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC9F5C,OAAA;gBACEiD,KAAK,EAAE9B,WAAW,CAACI,GAAI;gBACvB2B,QAAQ,EAAGC,CAAC,IAAK/B,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEI,GAAG,EAAE4B,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACvEI,IAAI,EAAE,CAAE;gBACRf,SAAS,EAAC;cAAoM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/M,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,iEAAiE;gBAAAC,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3G5C,OAAA;gBACEgD,IAAI,EAAC,MAAM;gBACXC,KAAK,EAAE9B,WAAW,CAACK,WAAY;gBAC/B0B,QAAQ,EAAGC,CAAC,IAAK/B,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEK,WAAW,EAAE2B,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBAC/EX,SAAS,EAAC;cAAwL;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5C,OAAA;cACEwC,OAAO,EAAEN,iBAAkB;cAC3BI,SAAS,EAAC,sLAAsL;cAAAC,QAAA,gBAEhMvC,OAAA,CAACJ,IAAI;gBAACiD,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,SAAS,KAAK,UAAU,iBACvBT,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvC,OAAA;YAAIsC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACrEvC,OAAA,CAACf,MAAM;cAACqD,SAAS,EAAC,gBAAgB;cAACO,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAEjD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL5C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvC,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAgB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxF5C,OAAA;gBAAKsC,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACvBvC,OAAA;kBACEgD,IAAI,EAAE/B,aAAa,GAAG,MAAM,GAAG,UAAW;kBAC1CgC,KAAK,EAAExB,YAAY,CAACE,OAAQ;kBAC5BuB,QAAQ,EAAGC,CAAC,IAAKzB,eAAe,CAAC;oBAAC,GAAGD,YAAY;oBAAEE,OAAO,EAAEwB,CAAC,CAACC,MAAM,CAACH;kBAAK,CAAC,CAAE;kBAC7EX,SAAS,EAAC;gBAAmH;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9H,CAAC,eACF5C,OAAA;kBACEgD,IAAI,EAAC,QAAQ;kBACbR,OAAO,EAAEA,CAAA,KAAMtB,gBAAgB,CAAC,CAACD,aAAa,CAAE;kBAChDqB,SAAS,EAAC,uFAAuF;kBAAAC,QAAA,EAEhGtB,aAAa,gBAAGjB,OAAA,CAACT,MAAM;oBAACsD,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACV,GAAG;oBAACuD,IAAI,EAAE;kBAAG;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN5C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpF5C,OAAA;gBACEgD,IAAI,EAAE/B,aAAa,GAAG,MAAM,GAAG,UAAW;gBAC1CgC,KAAK,EAAExB,YAAY,CAACG,GAAI;gBACxBsB,QAAQ,EAAGC,CAAC,IAAKzB,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEG,GAAG,EAAEuB,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBACzEX,SAAS,EAAC;cAA6G;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5C,OAAA;cAAAuC,QAAA,gBACEvC,OAAA;gBAAOsC,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5F5C,OAAA;gBACEgD,IAAI,EAAE/B,aAAa,GAAG,MAAM,GAAG,UAAW;gBAC1CgC,KAAK,EAAExB,YAAY,CAACI,OAAQ;gBAC5BqB,QAAQ,EAAGC,CAAC,IAAKzB,eAAe,CAAC;kBAAC,GAAGD,YAAY;kBAAEI,OAAO,EAAEsB,CAAC,CAACC,MAAM,CAACH;gBAAK,CAAC,CAAE;gBAC7EX,SAAS,EAAC;cAA6G;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN5C,OAAA;cACEwC,OAAO,EAAEH,oBAAqB;cAC9BC,SAAS,EAAC,0LAA0L;cAAAC,QAAA,gBAEpMvC,OAAA,CAACnB,IAAI;gBAACgE,IAAI,EAAE;cAAG;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,mBAEpB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,SAAS,KAAK,YAAY,iBACzBT,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvC,OAAA;YAAIsC,SAAS,EAAC,yEAAyE;YAAAC,QAAA,gBACrFvC,OAAA,CAACb,OAAO;cAACmD,SAAS,EAAC,iBAAiB;cAACO,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,cAEnD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL5C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvC,OAAA;cAAKsC,SAAS,EAAC,8EAA8E;cAAAC,QAAA,gBAC3FvC,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GACzChC,QAAQ,gBAAGP,OAAA,CAAClB,IAAI;kBAACwD,SAAS,EAAC,iBAAiB;kBAACO,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACjB,GAAG;kBAACuD,SAAS,EAAC,iBAAiB;kBAACO,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC1G5C,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAGsC,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAS;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACtE5C,OAAA;oBAAGsC,SAAS,EAAC,0CAA0C;oBAAAC,QAAA,EAAC;kBAAoB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEwC,OAAO,EAAEhC,cAAe;gBACxB8B,SAAS,EAAE,oDACT/B,QAAQ,GAAG,eAAe,GAAG,aAAa,EACzC;gBAAAgC,QAAA,eAEHvC,OAAA;kBAAKsC,SAAS,EAAE,uEACd/B,QAAQ,GAAG,eAAe,GAAG,iBAAiB;gBAC7C;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5C,OAAA;cAAKsC,SAAS,EAAC,4CAA4C;cAAAC,QAAA,gBACzDvC,OAAA;gBAAGsC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC9E5C,OAAA;gBAAKsC,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpC,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAACO,GAAG,CAACb,KAAK,iBAC5CjC,OAAA;kBAEEsC,SAAS,EAAE,gDAAgDL,KAAK,WAAWA,KAAK;gBAA4C,GADvHA,KAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEH,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,SAAS,KAAK,SAAS,iBACtBT,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvC,OAAA;YAAIsC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACrEvC,OAAA,CAACV,GAAG;cAACgD,SAAS,EAAC,iBAAiB;cAACO,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAE/C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL5C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBvC,OAAA;cAAKsC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC1EvC,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CvC,OAAA,CAACf,MAAM;kBAACqD,SAAS,EAAC,iBAAiB;kBAACO,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChD5C,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAGsC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAY;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzD5C,OAAA;oBAAGsC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAMxB,cAAc,CAAC,CAACD,WAAW,CAAE;gBAC5CuB,SAAS,EAAE,oDACTvB,WAAW,GAAG,eAAe,GAAG,aAAa,EAC5C;gBAAAwB,QAAA,eAEHvC,OAAA;kBAAKsC,SAAS,EAAE,uEACdvB,WAAW,GAAG,eAAe,GAAG,iBAAiB;gBAChD;kBAAA0B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAnC,SAAS,KAAK,eAAe,iBAC5BT,OAAA;UAAKsC,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBvC,OAAA;YAAIsC,SAAS,EAAC,yDAAyD;YAAAC,QAAA,gBACrEvC,OAAA,CAAChB,IAAI;cAACsD,SAAS,EAAC,eAAe;cAACO,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL5C,OAAA;YAAKsC,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBvC,OAAA;cAAKsC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC1EvC,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CvC,OAAA,CAAChB,IAAI;kBAACsD,SAAS,EAAC,eAAe;kBAACO,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5C5C,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAGsC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC/D5C,OAAA;oBAAGsC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAM5B,gBAAgB,CAAC,CAACD,aAAa,CAAE;gBAChD2B,SAAS,EAAE,oDACT3B,aAAa,GAAG,aAAa,GAAG,aAAa,EAC5C;gBAAA4B,QAAA,eAEHvC,OAAA;kBAAKsC,SAAS,EAAE,uEACd3B,aAAa,GAAG,eAAe,GAAG,iBAAiB;gBAClD;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5C,OAAA;cAAKsC,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC1EvC,OAAA;gBAAKsC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,GACzC1B,YAAY,gBAAGb,OAAA,CAACZ,OAAO;kBAACkD,SAAS,EAAC,eAAe;kBAACO,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5C,OAAA,CAACX,OAAO;kBAACiD,SAAS,EAAC,eAAe;kBAACO,IAAI,EAAE;gBAAG;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjH5C,OAAA;kBAAAuC,QAAA,gBACEvC,OAAA;oBAAGsC,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAa;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC1D5C,OAAA;oBAAGsC,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAC;kBAA4B;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN5C,OAAA;gBACEwC,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAC,CAACD,YAAY,CAAE;gBAC9CyB,SAAS,EAAE,oDACTzB,YAAY,GAAG,aAAa,GAAG,aAAa,EAC3C;gBAAA0B,QAAA,eAEHvC,OAAA;kBAAKsC,SAAS,EAAE,uEACdzB,YAAY,GAAG,eAAe,GAAG,iBAAiB;gBACjD;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN5C,OAAA;QAAKsC,SAAS,EAAC,oEAAoE;QAAAC,QAAA,eACjFvC,OAAA;UACEwC,OAAO,EAAElC,MAAO;UAChBgC,SAAS,EAAC,kLAAkL;UAAAC,QAAA,gBAE5LvC,OAAA,CAACL,MAAM;YAACkD,IAAI,EAAE;UAAG;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,YAEtB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAnWIH,aAAa;EAAA,QACQJ,OAAO,EACKC,QAAQ;AAAA;AAAAwD,EAAA,GAFzCrD,aAAa;AAqWnB,eAAeA,aAAa;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
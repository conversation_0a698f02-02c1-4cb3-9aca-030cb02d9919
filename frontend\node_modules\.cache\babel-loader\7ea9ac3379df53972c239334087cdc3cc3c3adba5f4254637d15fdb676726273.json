{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\me and her project\\\\frontend\\\\src\\\\components\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { Settings, Menu, X } from 'lucide-react';\nimport Chat from './Chat';\nimport Photos from './Photos';\nimport Notes from './Notes';\nimport SettingsPanel from './SettingsPanel';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('chat');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const {\n    user,\n    logout\n  } = useAuth();\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts({\n    '1': () => setActiveTab('chat'),\n    '2': () => setActiveTab('photos'),\n    '3': () => setActiveTab('notes'),\n    'escape': () => {\n      setShowSettings(false);\n      setShowMobileMenu(false);\n    },\n    's': () => setShowSettings(true)\n  });\n  const tabs = [{\n    id: 'chat',\n    name: 'Our Chats',\n    icon: '💬',\n    shortcut: '1',\n    gradient: 'from-pink-500 to-rose-500',\n    description: 'Sweet conversations'\n  }, {\n    id: 'photos',\n    name: 'Memories',\n    icon: '📸',\n    shortcut: '2',\n    gradient: 'from-purple-500 to-indigo-500',\n    description: 'Beautiful moments'\n  }, {\n    id: 'notes',\n    name: 'Love Notes',\n    icon: '💕',\n    shortcut: '3',\n    gradient: 'from-red-500 to-pink-500',\n    description: 'Heartfelt messages'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-10 left-10 w-32 h-32 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute top-32 right-20 w-40 h-40 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute bottom-32 left-1/4 w-28 h-28 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"relative z-10 backdrop-blur-sm bg-white/80 shadow-lg border-b border-pink-200/50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDC96\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                children: \"Our Love Story\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-gray-500 font-medium\",\n                children: \"Together forever \\u221E\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-right hidden sm:block\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 font-medium\",\n                children: \"Welcome back,\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent\",\n                children: [user === null || user === void 0 ? void 0 : user.name, \" \\uD83D\\uDC95\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowSettings(true),\n              className: \"p-3 text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80  rounded-full border border-gray-200 hover:border-gray-300  transition-all duration-200 hover:shadow-md focus:outline-none  focus:ring-2 focus:ring-pink-300 group\",\n              title: \"Settings (Press S)\",\n              children: /*#__PURE__*/_jsxDEV(Settings, {\n                size: 20,\n                className: \"group-hover:rotate-90 transition-transform duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowMobileMenu(!showMobileMenu),\n              className: \"sm:hidden p-3 text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80  rounded-full border border-gray-200 hover:border-gray-300  transition-all duration-200 hover:shadow-md\",\n              children: showMobileMenu ? /*#__PURE__*/_jsxDEV(X, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 35\n              }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"relative z-10 bg-white/60 backdrop-blur-sm border-b border-pink-100/50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`,\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setActiveTab(tab.id);\n              setShowMobileMenu(false);\n            },\n            className: `flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${activeTab === tab.id ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105` : 'text-gray-600 hover:text-gray-900 hover:bg-white/70 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl group-hover:scale-110 transition-transform duration-200\",\n              children: tab.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"font-bold\",\n                children: tab.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `text-xs ${activeTab === tab.id ? 'text-white/80' : 'text-gray-500'}`,\n                children: [tab.description, \" \\u2022 Press \", tab.shortcut]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this)]\n          }, tab.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 sm:px-0\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/50 p-8  transition-all duration-500 hover:shadow-3xl min-h-[600px]\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"transition-all duration-300\",\n            children: [activeTab === 'chat' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-lg\",\n                    children: \"\\uD83D\\uDCAC\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 156,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-800\",\n                  children: \"Our Sweet Conversations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Chat, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), activeTab === 'photos' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-lg\",\n                    children: \"\\uD83D\\uDCF8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 167,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-800\",\n                  children: \"Our Beautiful Memories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Photos, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), activeTab === 'notes' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-3 mb-6\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-white text-lg\",\n                    children: \"\\uD83D\\uDC95\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 178,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-bold text-gray-800\",\n                  children: \"Love Notes & Thoughts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Notes, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SettingsPanel, {\n      isOpen: showSettings,\n      onClose: () => setShowSettings(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"lq7mnmjBjraeL57iyuZICDUSbSM=\", false, function () {\n  return [useAuth, useKeyboardShortcuts];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useAuth", "Settings", "<PERSON><PERSON>", "X", "Cha<PERSON>", "Photos", "Notes", "SettingsPanel", "useKeyboardShortcuts", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "activeTab", "setActiveTab", "showSettings", "setShowSettings", "showMobileMenu", "setShowMobileMenu", "user", "logout", "1", "2", "3", "escape", "s", "tabs", "id", "name", "icon", "shortcut", "gradient", "description", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "size", "map", "tab", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/me and her project/frontend/src/components/Dashboard.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useAuth } from '../context/AuthContext';\nimport { Settings, Menu, X } from 'lucide-react';\nimport Chat from './Chat';\nimport Photos from './Photos';\nimport Notes from './Notes';\nimport SettingsPanel from './SettingsPanel';\nimport { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts';\n\nconst Dashboard = () => {\n  const [activeTab, setActiveTab] = useState('chat');\n  const [showSettings, setShowSettings] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  const { user, logout } = useAuth();\n\n  // Keyboard shortcuts\n  useKeyboardShortcuts({\n    '1': () => setActiveTab('chat'),\n    '2': () => setActiveTab('photos'),\n    '3': () => setActiveTab('notes'),\n    'escape': () => {\n      setShowSettings(false);\n      setShowMobileMenu(false);\n    },\n    's': () => setShowSettings(true)\n  });\n\n  const tabs = [\n    { \n      id: 'chat', \n      name: 'Our Chats', \n      icon: '💬', \n      shortcut: '1',\n      gradient: 'from-pink-500 to-rose-500',\n      description: 'Sweet conversations'\n    },\n    { \n      id: 'photos', \n      name: 'Memories', \n      icon: '📸', \n      shortcut: '2',\n      gradient: 'from-purple-500 to-indigo-500',\n      description: 'Beautiful moments'\n    },\n    { \n      id: 'notes', \n      name: 'Love Notes', \n      icon: '💕', \n      shortcut: '3',\n      gradient: 'from-red-500 to-pink-500',\n      description: 'Heartfelt messages'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-pink-50 via-rose-50 to-purple-50 relative overflow-hidden\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse\"></div>\n        <div className=\"absolute top-32 right-20 w-40 h-40 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-1000\"></div>\n        <div className=\"absolute bottom-32 left-1/4 w-28 h-28 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse delay-2000\"></div>\n      </div>\n\n      {/* Header */}\n      <header className=\"relative z-10 backdrop-blur-sm bg-white/80 shadow-lg border-b border-pink-200/50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center shadow-lg animate-pulse\">\n                <span className=\"text-2xl\">💖</span>\n              </div>\n              <div>\n                <h1 className=\"text-3xl font-bold bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent\">\n                  Our Love Story\n                </h1>\n                <p className=\"text-sm text-gray-500 font-medium\">Together forever ∞</p>\n              </div>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"text-right hidden sm:block\">\n                <p className=\"text-gray-700 font-medium\">Welcome back,</p>\n                <p className=\"text-lg font-bold bg-gradient-to-r from-pink-500 to-purple-500 bg-clip-text text-transparent\">\n                  {user?.name} 💕\n                </p>\n              </div>\n              \n              {/* Settings Button */}\n              <button\n                onClick={() => setShowSettings(true)}\n                className=\"p-3 text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 \n                         rounded-full border border-gray-200 hover:border-gray-300 \n                         transition-all duration-200 hover:shadow-md focus:outline-none \n                         focus:ring-2 focus:ring-pink-300 group\"\n                title=\"Settings (Press S)\"\n              >\n                <Settings size={20} className=\"group-hover:rotate-90 transition-transform duration-300\" />\n              </button>\n              \n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setShowMobileMenu(!showMobileMenu)}\n                className=\"sm:hidden p-3 text-gray-600 hover:text-gray-900 bg-white/50 hover:bg-white/80 \n                         rounded-full border border-gray-200 hover:border-gray-300 \n                         transition-all duration-200 hover:shadow-md\"\n              >\n                {showMobileMenu ? <X size={20} /> : <Menu size={20} />}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation Tabs */}\n      <nav className=\"relative z-10 bg-white/60 backdrop-blur-sm border-b border-pink-100/50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className={`flex space-x-8 overflow-x-auto py-4 ${showMobileMenu ? 'block' : 'hidden sm:flex'}`}>\n            {tabs.map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => {\n                  setActiveTab(tab.id);\n                  setShowMobileMenu(false);\n                }}\n                className={`flex items-center space-x-3 px-6 py-3 rounded-2xl font-medium transition-all duration-300 whitespace-nowrap group ${\n                  activeTab === tab.id\n                    ? `bg-gradient-to-r ${tab.gradient} text-white shadow-lg transform scale-105`\n                    : 'text-gray-600 hover:text-gray-900 hover:bg-white/70 hover:shadow-md'\n                }`}\n              >\n                <span className=\"text-2xl group-hover:scale-110 transition-transform duration-200\">\n                  {tab.icon}\n                </span>\n                <div className=\"text-left\">\n                  <div className=\"font-bold\">{tab.name}</div>\n                  <div className={`text-xs ${activeTab === tab.id ? 'text-white/80' : 'text-gray-500'}`}>\n                    {tab.description} • Press {tab.shortcut}\n                  </div>\n                </div>\n              </button>\n            ))}\n          </div>\n        </div>\n      </nav>\n\n      {/* Content */}\n      <main className=\"relative z-10 max-w-7xl mx-auto py-8 sm:px-6 lg:px-8\">\n        <div className=\"px-4 sm:px-0\">\n          <div className=\"bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/50 p-8 \n                         transition-all duration-500 hover:shadow-3xl min-h-[600px]\">\n            <div className=\"transition-all duration-300\">\n              {activeTab === 'chat' && (\n                <div className=\"animate-fade-in\">\n                  <div className=\"flex items-center space-x-3 mb-6\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-lg\">💬</span>\n                    </div>\n                    <h2 className=\"text-2xl font-bold text-gray-800\">Our Sweet Conversations</h2>\n                  </div>\n                  <Chat />\n                </div>\n              )}\n              {activeTab === 'photos' && (\n                <div className=\"animate-fade-in\">\n                  <div className=\"flex items-center space-x-3 mb-6\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-lg\">📸</span>\n                    </div>\n                    <h2 className=\"text-2xl font-bold text-gray-800\">Our Beautiful Memories</h2>\n                  </div>\n                  <Photos />\n                </div>\n              )}\n              {activeTab === 'notes' && (\n                <div className=\"animate-fade-in\">\n                  <div className=\"flex items-center space-x-3 mb-6\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white text-lg\">💕</span>\n                    </div>\n                    <h2 className=\"text-2xl font-bold text-gray-800\">Love Notes & Thoughts</h2>\n                  </div>\n                  <Notes />\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* Settings Panel */}\n      <SettingsPanel \n        isOpen={showSettings} \n        onClose={() => setShowSettings(false)} \n      />\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,QAAQ,EAAEC,IAAI,EAAEC,CAAC,QAAQ,cAAc;AAChD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,oBAAoB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,MAAM,CAAC;EAClD,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkB,cAAc,EAAEC,iBAAiB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM;IAAEoB,IAAI;IAAEC;EAAO,CAAC,GAAGpB,OAAO,CAAC,CAAC;;EAElC;EACAQ,oBAAoB,CAAC;IACnB,GAAG,EAAEa,CAAA,KAAMP,YAAY,CAAC,MAAM,CAAC;IAC/B,GAAG,EAAEQ,CAAA,KAAMR,YAAY,CAAC,QAAQ,CAAC;IACjC,GAAG,EAAES,CAAA,KAAMT,YAAY,CAAC,OAAO,CAAC;IAChC,QAAQ,EAAEU,CAAA,KAAM;MACdR,eAAe,CAAC,KAAK,CAAC;MACtBE,iBAAiB,CAAC,KAAK,CAAC;IAC1B,CAAC;IACD,GAAG,EAAEO,CAAA,KAAMT,eAAe,CAAC,IAAI;EACjC,CAAC,CAAC;EAEF,MAAMU,IAAI,GAAG,CACX;IACEC,EAAE,EAAE,MAAM;IACVC,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,2BAA2B;IACrCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,+BAA+B;IACzCC,WAAW,EAAE;EACf,CAAC,EACD;IACEL,EAAE,EAAE,OAAO;IACXC,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE,0BAA0B;IACpCC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEtB,OAAA;IAAKuB,SAAS,EAAC,+FAA+F;IAAAC,QAAA,gBAE5GxB,OAAA;MAAKuB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnExB,OAAA;QAAKuB,SAAS,EAAC;MAAuH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC7I5B,OAAA;QAAKuB,SAAS,EAAC;MAAqI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC3J5B,OAAA;QAAKuB,SAAS,EAAC;MAAsI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzJ,CAAC,eAGN5B,OAAA;MAAQuB,SAAS,EAAC,kFAAkF;MAAAC,QAAA,eAClGxB,OAAA;QAAKuB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDxB,OAAA;UAAKuB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDxB,OAAA;YAAKuB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxB,OAAA;cAAKuB,SAAS,EAAC,4HAA4H;cAAAC,QAAA,eACzIxB,OAAA;gBAAMuB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACN5B,OAAA;cAAAwB,QAAA,gBACExB,OAAA;gBAAIuB,SAAS,EAAC,+FAA+F;gBAAAC,QAAA,EAAC;cAE9G;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL5B,OAAA;gBAAGuB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5B,OAAA;YAAKuB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CxB,OAAA;cAAKuB,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCxB,OAAA;gBAAGuB,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC1D5B,OAAA;gBAAGuB,SAAS,EAAC,8FAA8F;gBAAAC,QAAA,GACxGf,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES,IAAI,EAAC,eACd;cAAA;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN5B,OAAA;cACE6B,OAAO,EAAEA,CAAA,KAAMvB,eAAe,CAAC,IAAI,CAAE;cACrCiB,SAAS,EAAC,wOAGsC;cAChDO,KAAK,EAAC,oBAAoB;cAAAN,QAAA,eAE1BxB,OAAA,CAACT,QAAQ;gBAACwC,IAAI,EAAE,EAAG;gBAACR,SAAS,EAAC;cAAyD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eAGT5B,OAAA;cACE6B,OAAO,EAAEA,CAAA,KAAMrB,iBAAiB,CAAC,CAACD,cAAc,CAAE;cAClDgB,SAAS,EAAC,uLAE2C;cAAAC,QAAA,EAEpDjB,cAAc,gBAAGP,OAAA,CAACP,CAAC;gBAACsC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAG5B,OAAA,CAACR,IAAI;gBAACuC,IAAI,EAAE;cAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGT5B,OAAA;MAAKuB,SAAS,EAAC,wEAAwE;MAAAC,QAAA,eACrFxB,OAAA;QAAKuB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDxB,OAAA;UAAKuB,SAAS,EAAE,uCAAuChB,cAAc,GAAG,OAAO,GAAG,gBAAgB,EAAG;UAAAiB,QAAA,EAClGR,IAAI,CAACgB,GAAG,CAAEC,GAAG,iBACZjC,OAAA;YAEE6B,OAAO,EAAEA,CAAA,KAAM;cACbzB,YAAY,CAAC6B,GAAG,CAAChB,EAAE,CAAC;cACpBT,iBAAiB,CAAC,KAAK,CAAC;YAC1B,CAAE;YACFe,SAAS,EAAE,qHACTpB,SAAS,KAAK8B,GAAG,CAAChB,EAAE,GAChB,oBAAoBgB,GAAG,CAACZ,QAAQ,2CAA2C,GAC3E,qEAAqE,EACxE;YAAAG,QAAA,gBAEHxB,OAAA;cAAMuB,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC/ES,GAAG,CAACd;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACP5B,OAAA;cAAKuB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxB,OAAA;gBAAKuB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EAAES,GAAG,CAACf;cAAI;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3C5B,OAAA;gBAAKuB,SAAS,EAAE,WAAWpB,SAAS,KAAK8B,GAAG,CAAChB,EAAE,GAAG,eAAe,GAAG,eAAe,EAAG;gBAAAO,QAAA,GACnFS,GAAG,CAACX,WAAW,EAAC,gBAAS,EAACW,GAAG,CAACb,QAAQ;cAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAnBDK,GAAG,CAAChB,EAAE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoBL,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN5B,OAAA;MAAMuB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,eACpExB,OAAA;QAAKuB,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BxB,OAAA;UAAKuB,SAAS,EAAC,4IAC2D;UAAAC,QAAA,eACxExB,OAAA;YAAKuB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACzCrB,SAAS,KAAK,MAAM,iBACnBH,OAAA;cAAKuB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxB,OAAA;gBAAKuB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxB,OAAA;kBAAKuB,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,eACjHxB,OAAA;oBAAMuB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN5B,OAAA;kBAAIuB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1E,CAAC,eACN5B,OAAA,CAACN,IAAI;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACN,EACAzB,SAAS,KAAK,QAAQ,iBACrBH,OAAA;cAAKuB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxB,OAAA;gBAAKuB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxB,OAAA;kBAAKuB,SAAS,EAAC,wGAAwG;kBAAAC,QAAA,eACrHxB,OAAA;oBAAMuB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN5B,OAAA;kBAAIuB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAsB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACN5B,OAAA,CAACL,MAAM;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CACN,EACAzB,SAAS,KAAK,OAAO,iBACpBH,OAAA;cAAKuB,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BxB,OAAA;gBAAKuB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBAC/CxB,OAAA;kBAAKuB,SAAS,EAAC,mGAAmG;kBAAAC,QAAA,eAChHxB,OAAA;oBAAMuB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACN5B,OAAA;kBAAIuB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE,CAAC,eACN5B,OAAA,CAACJ,KAAK;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP5B,OAAA,CAACH,aAAa;MACZqC,MAAM,EAAE7B,YAAa;MACrB8B,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC,KAAK;IAAE;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC1B,EAAA,CA3LID,SAAS;EAAA,QAIYX,OAAO,EAGhCQ,oBAAoB;AAAA;AAAAsC,EAAA,GAPhBnC,SAAS;AA6Lf,eAAeA,SAAS;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}